<?php
/**
 * Configuración global del IPTV Web Player
 */

// Configuración del servidor Xtream Codes (configurable por el cliente)
define('XTREAM_SERVER_URL', 'http://tu-servidor.com:puerto');

// Configuración de la aplicación
define('APP_NAME', 'IPTV Web Player');
define('APP_VERSION', '1.0.0');
define('APP_DEBUG', false);

// Headers de seguridad para permitir contenido mixto y streaming
$isHttps = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on';

if ($isHttps) {
    // En HTTPS, usar política más estricta pero permitir proxy local
    header("Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob:; media-src 'self' blob: data:; connect-src 'self'; font-src 'self' data:; object-src 'none'; base-uri 'self'; frame-ancestors 'none';");
} else {
    // En HTTP, permitir contenido mixto con upgrade automático
    header('Content-Security-Policy: upgrade-insecure-requests; default-src * data: blob:; script-src * \'unsafe-inline\' \'unsafe-eval\'; style-src * \'unsafe-inline\';');
}

header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS, HEAD');
header('Access-Control-Allow-Headers: Origin, Content-Type, Accept, Authorization, Range');

// Configuración de sesiones
if (session_status() === PHP_SESSION_NONE) {
    ini_set('session.cookie_httponly', 1);
    ini_set('session.cookie_secure', 0); // Cambiado a 0 para permitir HTTP
    ini_set('session.use_only_cookies', 1);
    ini_set('session.cookie_samesite', 'Lax'); // Cambiado a Lax para mejor compatibilidad
}

// Configuración de timeouts
define('API_TIMEOUT', 30);
define('SESSION_TIMEOUT', 3600); // 1 hora

// Configuración de cache
define('CACHE_ENABLED', true);
define('CACHE_DURATION', 300); // 5 minutos

// Configuración de logs
define('LOG_ENABLED', false);
define('LOG_FILE', __DIR__ . '/logs/app.log');

// Configuración de límites
define('MAX_SEARCH_RESULTS', 100);
define('MAX_EPISODES_PER_SEASON', 50);

// Headers de seguridad
if (!headers_sent()) {
    header('X-Content-Type-Options: nosniff');
    header('X-Frame-Options: DENY');
    header('X-XSS-Protection: 1; mode=block');
    header('Referrer-Policy: strict-origin-when-cross-origin');
    
    // Solo agregar HSTS si estamos en HTTPS
    if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
        header('Strict-Transport-Security: max-age=31536000; includeSubDomains');
    }
}

// Función para logging
function logMessage($message, $level = 'INFO') {
    if (!LOG_ENABLED) return;
    
    $timestamp = date('Y-m-d H:i:s');
    $logEntry = "[{$timestamp}] [{$level}] {$message}" . PHP_EOL;
    
    // Crear directorio de logs si no existe
    $logDir = dirname(LOG_FILE);
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    file_put_contents(LOG_FILE, $logEntry, FILE_APPEND | LOCK_EX);
}

// Función para debug
function debugLog($data, $label = 'DEBUG') {
    if (APP_DEBUG) {
        logMessage($label . ': ' . json_encode($data), 'DEBUG');
    }
}

// Función para manejar errores
function handleError($message, $code = 500) {
    logMessage("Error {$code}: {$message}", 'ERROR');
    
    if (APP_DEBUG) {
        echo json_encode(['error' => $message, 'code' => $code]);
    } else {
        echo json_encode(['error' => 'Ha ocurrido un error interno']);
    }
    
    http_response_code($code);
    exit();
}

// Función para validar entrada
function sanitizeInput($input) {
    if (is_array($input)) {
        return array_map('sanitizeInput', $input);
    }
    
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

// Función para validar URL
function isValidUrl($url) {
    return filter_var($url, FILTER_VALIDATE_URL) !== false;
}

// Función para validar email
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

// Función para generar token CSRF
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

// Función para verificar token CSRF
function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

// Configuración de zona horaria
date_default_timezone_set('America/Mexico_City');

// Configuración de codificación
mb_internal_encoding('UTF-8');

// Configurar manejo de errores
if (APP_DEBUG) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// Función para formatear tiempo
function formatDuration($seconds) {
    if (!is_numeric($seconds) || $seconds < 0) {
        return '00:00';
    }
    
    $hours = floor($seconds / 3600);
    $minutes = floor(($seconds % 3600) / 60);
    $seconds = $seconds % 60;
    
    if ($hours > 0) {
        return sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);
    } else {
        return sprintf('%02d:%02d', $minutes, $seconds);
    }
}

// Función para obtener tamaño de archivo legible
function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    $unitIndex = 0;
    
    while ($bytes >= 1024 && $unitIndex < count($units) - 1) {
        $bytes /= 1024;
        $unitIndex++;
    }
    
    return round($bytes, 2) . ' ' . $units[$unitIndex];
}

// Función para verificar si es petición AJAX
function isAjaxRequest() {
    return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
           strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
}

// Función para obtener IP del cliente
function getClientIP() {
    $ipKeys = ['HTTP_CF_CONNECTING_IP', 'HTTP_X_FORWARDED_FOR', 'HTTP_X_FORWARDED', 
               'HTTP_X_CLUSTER_CLIENT_IP', 'HTTP_FORWARDED_FOR', 'HTTP_FORWARDED', 'REMOTE_ADDR'];
    
    foreach ($ipKeys as $key) {
        if (isset($_SERVER[$key]) && !empty($_SERVER[$key])) {
            $ips = explode(',', $_SERVER[$key]);
            $ip = trim($ips[0]);
            
            if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                return $ip;
            }
        }
    }
    
    return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
}

// Función para rate limiting básico
function checkRateLimit($identifier, $maxRequests = 60, $timeWindow = 3600) {
    $key = 'rate_limit_' . md5($identifier);
    
    if (!isset($_SESSION[$key])) {
        $_SESSION[$key] = ['count' => 0, 'reset_time' => time() + $timeWindow];
    }
    
    $rateData = $_SESSION[$key];
    
    // Reset si ha pasado el tiempo
    if (time() > $rateData['reset_time']) {
        $_SESSION[$key] = ['count' => 0, 'reset_time' => time() + $timeWindow];
        $rateData = $_SESSION[$key];
    }
    
    // Verificar límite
    if ($rateData['count'] >= $maxRequests) {
        return false;
    }
    
    // Incrementar contador
    $_SESSION[$key]['count']++;
    
    return true;
}

// Inicializar logs
if (LOG_ENABLED) {
    logMessage('Application started - IP: ' . getClientIP(), 'INFO');
}
?>
