<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API Completo</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            max-width: 1000px; 
            margin: 20px auto; 
            padding: 20px; 
            background: #1a1a1a; 
            color: white; 
        }
        .test-section {
            background: #2d2d2d;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .test-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-btn:hover { background: #0056b3; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
        .result {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🧪 Test Completo de APIs</h1>
    
    <div class="test-section">
        <h2>1. Test TV en Vivo</h2>
        <button class="test-btn" onclick="testLiveCategories()">Categorías TV</button>
        <button class="test-btn" onclick="testLiveStreams()">Canales TV</button>
        <div id="live-result" class="result">Listo para probar...</div>
    </div>

    <div class="test-section">
        <h2>2. Test Películas</h2>
        <button class="test-btn" onclick="testMovieCategories()">Categorías Películas</button>
        <button class="test-btn" onclick="testMovies()">Películas</button>
        <div id="movies-result" class="result">Listo para probar...</div>
    </div>

    <div class="test-section">
        <h2>3. Test Series</h2>
        <button class="test-btn" onclick="testSeriesCategories()">Categorías Series</button>
        <button class="test-btn" onclick="testSeries()">Series</button>
        <div id="series-result" class="result">Listo para probar...</div>
    </div>

    <div class="test-section">
        <h2>4. Test Stream URLs</h2>
        <button class="test-btn" onclick="testStreamURL()">URL de Stream</button>
        <div id="stream-result" class="result">Listo para probar...</div>
    </div>

    <script>
        let firstLiveCategory = null;
        let firstMovieCategory = null;
        let firstSeriesCategory = null;
        let firstChannel = null;

        function log(section, message, type = 'info') {
            const result = document.getElementById(section + '-result');
            const timestamp = new Date().toLocaleTimeString();
            const colors = { info: '#17a2b8', success: '#28a745', error: '#dc3545' };
            result.innerHTML += `<span style="color: ${colors[type]}">[${timestamp}] ${message}</span>\n`;
            result.scrollTop = result.scrollHeight;
        }

        async function testLiveCategories() {
            log('live', 'Probando categorías de TV...', 'info');
            try {
                const response = await fetch('/api/xtream.php?action=get_live_categories');
                if (!response.ok) throw new Error(`HTTP ${response.status}`);
                
                const data = await response.json();
                if (data.error) throw new Error(data.error);
                
                if (Array.isArray(data)) {
                    firstLiveCategory = data[0];
                    log('live', `✅ ${data.length} categorías encontradas`, 'success');
                    log('live', `Primera categoría: ${data[0].category_name} (ID: ${data[0].category_id})`, 'info');
                } else {
                    log('live', '❌ Respuesta no es un array', 'error');
                }
            } catch (error) {
                log('live', `❌ Error: ${error.message}`, 'error');
            }
        }

        async function testLiveStreams() {
            if (!firstLiveCategory) {
                log('live', '⚠️ Ejecuta primero "Categorías TV"', 'error');
                return;
            }
            
            log('live', `Probando canales de ${firstLiveCategory.category_name}...`, 'info');
            try {
                const response = await fetch(`/api/xtream.php?action=get_live_streams&category_id=${firstLiveCategory.category_id}`);
                if (!response.ok) throw new Error(`HTTP ${response.status}`);
                
                const data = await response.json();
                if (data.error) throw new Error(data.error);
                
                if (Array.isArray(data)) {
                    firstChannel = data[0];
                    log('live', `✅ ${data.length} canales encontrados`, 'success');
                    log('live', `Primer canal: ${data[0].name} (ID: ${data[0].stream_id})`, 'info');
                } else {
                    log('live', '❌ Respuesta no es un array', 'error');
                }
            } catch (error) {
                log('live', `❌ Error: ${error.message}`, 'error');
            }
        }

        async function testMovieCategories() {
            log('movies', 'Probando categorías de películas...', 'info');
            try {
                const response = await fetch('/api/xtream.php?action=get_movie_categories');
                if (!response.ok) throw new Error(`HTTP ${response.status}`);
                
                const data = await response.json();
                if (data.error) throw new Error(data.error);
                
                if (Array.isArray(data)) {
                    firstMovieCategory = data[0];
                    log('movies', `✅ ${data.length} categorías encontradas`, 'success');
                    if (data.length > 0) {
                        log('movies', `Primera categoría: ${data[0].category_name} (ID: ${data[0].category_id})`, 'info');
                    }
                } else {
                    log('movies', '❌ Respuesta no es un array', 'error');
                }
            } catch (error) {
                log('movies', `❌ Error: ${error.message}`, 'error');
            }
        }

        async function testMovies() {
            if (!firstMovieCategory) {
                log('movies', '⚠️ Ejecuta primero "Categorías Películas"', 'error');
                return;
            }
            
            log('movies', `Probando películas de ${firstMovieCategory.category_name}...`, 'info');
            try {
                const response = await fetch(`/api/xtream.php?action=get_movies&category_id=${firstMovieCategory.category_id}`);
                if (!response.ok) throw new Error(`HTTP ${response.status}`);
                
                const data = await response.json();
                if (data.error) throw new Error(data.error);
                
                if (Array.isArray(data)) {
                    log('movies', `✅ ${data.length} películas encontradas`, 'success');
                    if (data.length > 0) {
                        log('movies', `Primera película: ${data[0].name} (ID: ${data[0].stream_id})`, 'info');
                    }
                } else {
                    log('movies', '❌ Respuesta no es un array', 'error');
                }
            } catch (error) {
                log('movies', `❌ Error: ${error.message}`, 'error');
            }
        }

        async function testSeriesCategories() {
            log('series', 'Probando categorías de series...', 'info');
            try {
                const response = await fetch('/api/xtream.php?action=get_series_categories');
                if (!response.ok) throw new Error(`HTTP ${response.status}`);
                
                const data = await response.json();
                if (data.error) throw new Error(data.error);
                
                if (Array.isArray(data)) {
                    firstSeriesCategory = data[0];
                    log('series', `✅ ${data.length} categorías encontradas`, 'success');
                    if (data.length > 0) {
                        log('series', `Primera categoría: ${data[0].category_name} (ID: ${data[0].category_id})`, 'info');
                    }
                } else {
                    log('series', '❌ Respuesta no es un array', 'error');
                }
            } catch (error) {
                log('series', `❌ Error: ${error.message}`, 'error');
            }
        }

        async function testSeries() {
            if (!firstSeriesCategory) {
                log('series', '⚠️ Ejecuta primero "Categorías Series"', 'error');
                return;
            }
            
            log('series', `Probando series de ${firstSeriesCategory.category_name}...`, 'info');
            try {
                const response = await fetch(`/api/xtream.php?action=get_series&category_id=${firstSeriesCategory.category_id}`);
                if (!response.ok) throw new Error(`HTTP ${response.status}`);
                
                const data = await response.json();
                if (data.error) throw new Error(data.error);
                
                if (Array.isArray(data)) {
                    log('series', `✅ ${data.length} series encontradas`, 'success');
                    if (data.length > 0) {
                        log('series', `Primera serie: ${data[0].name} (ID: ${data[0].series_id})`, 'info');
                    }
                } else {
                    log('series', '❌ Respuesta no es un array', 'error');
                }
            } catch (error) {
                log('series', `❌ Error: ${error.message}`, 'error');
            }
        }

        async function testStreamURL() {
            if (!firstChannel) {
                log('stream', '⚠️ Ejecuta primero "Canales TV"', 'error');
                return;
            }
            
            log('stream', `Probando URL de stream para ${firstChannel.name}...`, 'info');
            try {
                const response = await fetch(`/api/xtream.php?action=get_stream_url&stream_id=${firstChannel.stream_id}&type=live`);
                if (!response.ok) throw new Error(`HTTP ${response.status}`);
                
                const data = await response.json();
                if (data.error) throw new Error(data.error);
                
                if (data.success && data.url) {
                    log('stream', `✅ URL generada exitosamente`, 'success');
                    log('stream', `URL: ${data.url}`, 'info');
                } else {
                    log('stream', `❌ No se pudo generar URL: ${data.message || 'Error desconocido'}`, 'error');
                }
            } catch (error) {
                log('stream', `❌ Error: ${error.message}`, 'error');
            }
        }

        // Auto-start
        window.onload = function() {
            log('live', 'Test API iniciado', 'success');
            log('movies', 'Test API iniciado', 'success');
            log('series', 'Test API iniciado', 'success');
            log('stream', 'Test API iniciado', 'success');
        };
    </script>
</body>
</html>
