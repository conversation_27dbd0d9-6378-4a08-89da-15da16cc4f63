# Habilitar reescritura de URLs
RewriteEngine On

# COMENTADO: Redirigir HTTP a HTTPS para permitir contenido mixto
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Configuración de headers de seguridad (modificado para streaming)
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options SAMEORIGIN
Header always set X-XSS-Protection "1; mode=block"
# COMENTADO para permitir contenido mixto
# Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
Header always set Referrer-Policy "strict-origin-when-cross-origin"

# Configuración CSP dinámica manejada por PHP
# Header always set Content-Security-Policy "default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http: https: *;"

# Configuración CORS para videos y streaming (ampliado)
<FilesMatch "\.(mp4|webm|ogg|avi|mkv|mov|m4v|3gp|ts|m3u8|flv)$">
    Header set Access-Control-Allow-Origin "*"
    Header set Access-Control-Allow-Methods "GET, OPTIONS, HEAD"
    Header set Access-Control-Allow-Headers "Range, Content-Type, Accept-Encoding, Authorization"
    Header set Access-Control-Expose-Headers "Content-Length, Content-Range, Accept-Ranges"
    Header set Accept-Ranges "bytes"
</FilesMatch>

# Configuración especial para el proxy de streams
<Files "stream_proxy.php">
    Header set Access-Control-Allow-Origin "*"
    Header set Access-Control-Allow-Methods "GET, OPTIONS, HEAD"
    Header set Access-Control-Allow-Headers "Range, Content-Type, Accept-Encoding, Authorization"
    Header set Access-Control-Expose-Headers "Content-Length, Content-Range, Accept-Ranges"
</Files>

# Cache para recursos estáticos
<FilesMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$">
    ExpiresActive On
    ExpiresDefault "access plus 1 month"
    Header set Cache-Control "public, immutable"
</FilesMatch>

# No cache para archivos PHP
<FilesMatch "\.(php)$">
    Header set Cache-Control "no-cache, no-store, must-revalidate"
    Header set Pragma "no-cache"
    Header set Expires "0"
</FilesMatch>

# Proteger archivos de configuración
<FilesMatch "^\.">
    Order allow,deny
    Deny from all
</FilesMatch>

# Proteger archivos sensibles
<FilesMatch "\.(env|log|ini|conf|config)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Configuración de tipos MIME para streaming
AddType video/mp4 .mp4
AddType video/webm .webm
AddType video/ogg .ogv
AddType application/vnd.apple.mpegurl .m3u8
AddType video/mp2t .ts

# Compresión gzip
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# Configuración de límites
php_value upload_max_filesize 100M
php_value post_max_size 100M
php_value max_execution_time 300
php_value max_input_time 300
php_value memory_limit 256M

# Página de error personalizada
ErrorDocument 404 /404.html
ErrorDocument 500 /500.html

# Redirigir al login si no hay sesión activa
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !^/(login\.php|assets/|api/|404\.html|500\.html)
RewriteCond %{HTTP_COOKIE} !PHPSESSID [NC]
RewriteRule ^.*$ /login.php [L,R=302]
