document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('login-form');
    const serverUrlInput = document.getElementById('server_url');
    const usernameInput = document.getElementById('username');
    const passwordInput = document.getElementById('password');
    const loginButton = loginForm.querySelector('.login-btn');
    const loginMessage = document.getElementById('login-message');
    const loginLoading = document.getElementById('login-loading');

    // Focus en el primer input
    serverUrlInput.focus();

    // Manejar envío del formulario
    loginForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const serverUrl = serverUrlInput.value.trim();
        const username = usernameInput.value.trim();
        const password = passwordInput.value.trim();

        // Validar campos
        if (!serverUrl || !username || !password) {
            showMessage('Por favor, completa todos los campos.', 'error');
            return;
        }

        // Mostrar loading
        showLoading(true);
        hideMessage();

        // Enviar datos de login
        const formData = new FormData();
        formData.append('server_url', serverUrl);
        formData.append('username', username);
        formData.append('password', password);

        fetch('login.php', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            // Verificar si la respuesta es JSON válida
            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json')) {
                throw new Error('El servidor no devolvió una respuesta JSON válida');
            }
            return response.json();
        })
        .then(data => {
            showLoading(false);
            
            if (data.success) {
                showMessage('¡Login exitoso! Redirigiendo...', 'success');
                setTimeout(() => {
                    window.location.href = data.redirect || 'index.php';
                }, 1000);
            } else {
                showMessage(data.message || 'Error de autenticación', 'error');
                // Limpiar contraseña en caso de error
                passwordInput.value = '';
                passwordInput.focus();
            }
        })
        .catch(error => {
            showLoading(false);
            console.error('Error:', error);
            
            // Mensaje de error más específico
            let errorMessage = 'Error de conexión. Inténtalo de nuevo.';
            if (error.message.includes('JSON')) {
                errorMessage = 'Error de comunicación con el servidor. Verifica que el servidor esté funcionando correctamente.';
            }
            
            showMessage(errorMessage, 'error');
            passwordInput.value = '';
            passwordInput.focus();
        });
    });

    // Validación en tiempo real
    usernameInput.addEventListener('input', function() {
        validateInput(this);
    });

    passwordInput.addEventListener('input', function() {
        validateInput(this);
    });

    // Enter para enviar formulario
    usernameInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            passwordInput.focus();
        }
    });

    passwordInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            loginForm.dispatchEvent(new Event('submit'));
        }
    });

    // Funciones auxiliares
    function validateInput(input) {
        const inputGroup = input.parentElement;
        
        if (input.value.trim()) {
            inputGroup.classList.remove('error');
            inputGroup.classList.add('success');
        } else {
            inputGroup.classList.remove('success');
            inputGroup.classList.add('error');
        }
    }

    function showMessage(message, type) {
        loginMessage.textContent = message;
        loginMessage.className = `message ${type}`;
        loginMessage.style.display = 'block';
        
        // Auto-hide después de 5 segundos si es error
        if (type === 'error') {
            setTimeout(() => {
                hideMessage();
            }, 5000);
        }
    }

    function hideMessage() {
        loginMessage.style.display = 'none';
    }

    function showLoading(show) {
        if (show) {
            loginLoading.style.display = 'block';
            loginButton.disabled = true;
            loginButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Verificando...';
        } else {
            loginLoading.style.display = 'none';
            loginButton.disabled = false;
            loginButton.innerHTML = '<i class="fas fa-sign-in-alt"></i> Iniciar Sesión';
        }
    }

    // Efecto de ripple en el botón
    loginButton.addEventListener('click', function(e) {
        const ripple = document.createElement('span');
        const rect = this.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = e.clientX - rect.left - size / 2;
        const y = e.clientY - rect.top - size / 2;
        
        ripple.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            left: ${x}px;
            top: ${y}px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: scale(0);
            animation: ripple 0.6s linear;
            pointer-events: none;
        `;
        
        this.style.position = 'relative';
        this.style.overflow = 'hidden';
        this.appendChild(ripple);
        
        setTimeout(() => {
            ripple.remove();
        }, 600);
    });

    // CSS para la animación del ripple
    const style = document.createElement('style');
    style.textContent = `
        @keyframes ripple {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }
    `;
    document.head.appendChild(style);
});
