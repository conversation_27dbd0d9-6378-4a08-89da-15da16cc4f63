<?php
session_start();
require_once 'includes/config.php';

// Si ya está logueado, redirigir al dashboard
if (isset($_SESSION['user_info']) && isset($_SESSION['server_info'])) {
    header('Location: index.php');
    exit();
}

// Manejar petición AJAX de login
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['username']) && isset($_POST['password']) && isset($_POST['server_url'])) {
    // Configurar headers para JSON
    header('Content-Type: application/json');

    $server_url = sanitizeInput($_POST['server_url']);
    $username = sanitizeInput($_POST['username']);
    $password = sanitizeInput($_POST['password']);

    if (empty($server_url) || empty($username) || empty($password)) {
        echo json_encode(['success' => false, 'message' => 'Por favor, completa todos los campos.']);
        exit();
    }

    try {
        // Realizar autenticación con Xtream Codes API
        require_once 'includes/xtream_api.php';

        $api = new XtreamAPI();
        $api->setServerUrl($server_url); // Configurar servidor dinámicamente
        $auth_result = $api->authenticate($username, $password);

        if ($auth_result['success']) {
            $_SESSION['user_info'] = $auth_result['user_info'];
            $_SESSION['server_info'] = ['url' => $server_url]; // Guardar URL configurada

            logMessage("User {$username} logged in successfully from IP: " . getClientIP(), 'INFO');

            echo json_encode(['success' => true, 'redirect' => 'index.php']);
        } else {
            logMessage("Failed login attempt for user {$username} from IP: " . getClientIP(), 'WARNING');
            echo json_encode(['success' => false, 'message' => $auth_result['message']]);
        }
    } catch (Exception $e) {
        logMessage("Login error for user {$username}: " . $e->getMessage(), 'ERROR');
        echo json_encode(['success' => false, 'message' => 'Error de conexión con el servidor']);
    }
    
    exit();
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - IPTV Web Player</title>
    <link rel="icon" href="favicon.ico" type="image/x-icon">
    <link rel="stylesheet" href="assets/css/login.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="login-container">
        <div class="login-form">
            <div class="logo">
                <i class="fas fa-tv"></i>
                <h1>IPTV Player</h1>
            </div>
            
            <form id="login-form" method="POST">
                <div class="form-group">
                    <label for="server_url">Servidor</label>
                    <div class="input-group">
                        <i class="fas fa-server"></i>
                        <input type="text" id="server_url" name="server_url" required placeholder="http://tu-servidor.com:puerto">
                    </div>
                </div>

                <div class="form-group">
                    <label for="username">Usuario</label>
                    <div class="input-group">
                        <i class="fas fa-user"></i>
                        <input type="text" id="username" name="username" required placeholder="Ingresa tu usuario">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="password">Contraseña</label>
                    <div class="input-group">
                        <i class="fas fa-lock"></i>
                        <input type="password" id="password" name="password" required placeholder="Ingresa tu contraseña">
                    </div>
                </div>
                
                <button type="submit" class="login-btn">
                    <i class="fas fa-sign-in-alt"></i> Iniciar Sesión
                </button>
            </form>
            
            <div id="login-message" class="message" style="display: none;"></div>
            
            <div class="loading" id="login-loading" style="display: none;">
                <i class="fas fa-spinner fa-spin"></i>
                <span>Verificando credenciales...</span>
            </div>
        </div>
    </div>

    <script src="assets/js/login.js"></script>
</body>
</html>
