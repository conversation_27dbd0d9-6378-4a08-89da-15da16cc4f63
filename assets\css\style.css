/* Reset y estilos base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: #ffffff;
    line-height: 1.6;
    min-height: 100vh;
}

.container {
    max-width: 1920px;
    margin: 0 auto;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.header {
    background: rgba(0, 0, 0, 0.8);
    padding: 1rem 2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header h1 {
    font-size: 1.8rem;
    color: #4CAF50;
}

.header h1 i {
    margin-right: 10px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-info span {
    font-weight: 500;
}

.logout-btn {
    background: #f44336;
    color: white;
    padding: 0.5rem 1rem;
    text-decoration: none;
    border-radius: 5px;
    transition: background 0.3s;
}

.logout-btn:hover {
    background: #d32f2f;
}

/* Navigation */
.main-nav {
    background: rgba(0, 0, 0, 0.6);
    padding: 0 2rem;
}

.nav-tabs {
    display: flex;
    list-style: none;
    gap: 0;
}

.nav-item {
    padding: 1rem 2rem;
    cursor: pointer;
    transition: all 0.3s;
    border-bottom: 3px solid transparent;
    font-weight: 500;
}

.nav-item:hover {
    background: rgba(255, 255, 255, 0.1);
}

.nav-item.active {
    background: rgba(76, 175, 80, 0.2);
    border-bottom-color: #4CAF50;
}

.nav-item i {
    margin-right: 8px;
}

/* Main Content */
.main-content {
    flex: 1;
    padding: 2rem;
}

.content-section {
    display: none;
}

.content-section.active {
    display: block;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.section-header h2 {
    font-size: 2rem;
    margin-bottom: 0;
}

/* Search Container */
.search-container {
    position: relative;
    max-width: 400px;
}

.search-container input {
    width: 100%;
    padding: 0.75rem 1rem;
    padding-right: 3rem;
    border: none;
    border-radius: 25px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 1rem;
}

.search-container input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.search-container i {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: rgba(255, 255, 255, 0.7);
}

/* Categories Grid */
.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.category-card {
    background: rgba(255, 255, 255, 0.1);
    padding: 1.5rem;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s;
    text-align: center;
    border: 2px solid transparent;
}

.category-card:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: #4CAF50;
    transform: translateY(-5px);
}

.category-card h3 {
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
}

.category-card .channel-count {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
}

/* Channels Grid */
.channels-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1rem;
}

.channel-card {
    background: rgba(255, 255, 255, 0.1);
    padding: 1rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.channel-card:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.channel-logo {
    width: 50px;
    height: 50px;
    border-radius: 5px;
    object-fit: cover;
    background: rgba(255, 255, 255, 0.1);
}

.channel-info h4 {
    font-size: 1rem;
    margin-bottom: 0.25rem;
}

.channel-info .channel-category {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.7);
}

/* Movies Grid */
.movies-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1.5rem;
}

.movie-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s;
}

.movie-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.movie-poster {
    width: 100%;
    height: 300px;
    object-fit: cover;
    background: rgba(255, 255, 255, 0.1);
}

.movie-info {
    padding: 1rem;
}

.movie-info h4 {
    font-size: 1rem;
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.movie-year {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

.movie-rating {
    color: #FFD700;
    font-size: 0.9rem;
    margin-top: 0.25rem;
}

/* Series Grid */
.series-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1.5rem;
}

.series-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s;
}

.series-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.series-poster {
    width: 100%;
    height: 300px;
    object-fit: cover;
    background: rgba(255, 255, 255, 0.1);
}

.series-info {
    padding: 1rem;
}

.series-info h4 {
    font-size: 1rem;
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.series-year {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

.series-seasons {
    color: #4CAF50;
    font-size: 0.9rem;
    margin-top: 0.25rem;
}

/* Loading */
.loading {
    text-align: center;
    padding: 3rem;
    font-size: 1.2rem;
}

.loading i {
    margin-right: 10px;
    font-size: 1.5rem;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
}

.modal-content {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    margin: 2% auto;
    padding: 0;
    border-radius: 10px;
    width: 95%;
    max-width: 1200px;
    max-height: 90vh;
    overflow: hidden;
    position: relative;
}

.modal-header {
    background: rgba(0, 0, 0, 0.8);
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h3 {
    margin: 0;
    font-size: 1.3rem;
}

.close-modal {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s;
}

.close-modal:hover {
    color: #fff;
}

.modal-body {
    padding: 0;
    max-height: calc(90vh - 80px);
    overflow-y: auto;
}

/* Video Container */
.video-container {
    position: relative;
    background: #000;
}

#main-video {
    width: 100%;
    height: auto;
    min-height: 400px;
    max-height: 70vh;
}

/* Custom Controls */
.custom-controls {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    padding: 1rem;
    opacity: 0;
    transition: opacity 0.3s;
}

.video-container:hover .custom-controls {
    opacity: 1;
}

.controls-row {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 0.5rem;
}

.controls-row button {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    padding: 0.5rem;
    border-radius: 5px;
    cursor: pointer;
    transition: background 0.3s;
    font-size: 1rem;
}

.controls-row button:hover {
    background: rgba(255, 255, 255, 0.3);
}

.volume-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

#volume-slider {
    width: 100px;
}

.time-display {
    color: white;
    font-size: 0.9rem;
}

.right-controls {
    margin-left: auto;
    display: flex;
    gap: 0.5rem;
}

.progress-container {
    margin-top: 0.5rem;
}

#progress-slider {
    width: 100%;
    height: 5px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 5px;
    outline: none;
    cursor: pointer;
}

/* Episode List */
.episode-list {
    background: rgba(0, 0, 0, 0.3);
    padding: 1rem;
    max-height: 300px;
    overflow-y: auto;
}

.episode-item {
    background: rgba(255, 255, 255, 0.1);
    padding: 1rem;
    margin-bottom: 0.5rem;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.episode-item:hover {
    background: rgba(255, 255, 255, 0.2);
}

.episode-item.active {
    background: rgba(76, 175, 80, 0.3);
    border-left: 4px solid #4CAF50;
}

.episode-number {
    background: #4CAF50;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 15px;
    font-size: 0.8rem;
    min-width: 60px;
    text-align: center;
}

.episode-title {
    flex: 1;
    font-weight: 500;
}

.episode-duration {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

/* Detail Content */
.detail-content {
    padding: 2rem;
}

.detail-header {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
}

.detail-poster {
    width: 300px;
    height: 450px;
    object-fit: cover;
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.1);
}

.detail-info {
    flex: 1;
}

.detail-info h2 {
    font-size: 2rem;
    margin-bottom: 1rem;
}

.detail-meta {
    display: flex;
    gap: 2rem;
    margin-bottom: 1rem;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
}

.detail-rating {
    color: #FFD700;
    font-size: 1.1rem;
    margin-bottom: 1rem;
}

.detail-description {
    line-height: 1.6;
    margin-bottom: 2rem;
}

.play-button {
    background: #4CAF50;
    color: white;
    padding: 1rem 2rem;
    border: none;
    border-radius: 25px;
    font-size: 1.1rem;
    cursor: pointer;
    transition: background 0.3s;
    margin-right: 1rem;
}

.play-button:hover {
    background: #45a049;
}

.play-button i {
    margin-right: 8px;
}

/* Seasons */
.seasons-container {
    margin-top: 2rem;
}

.seasons-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.season-card {
    background: rgba(255, 255, 255, 0.1);
    padding: 1rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s;
    text-align: center;
    border: 2px solid transparent;
}

.season-card:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: #4CAF50;
}

.season-card.active {
    background: rgba(76, 175, 80, 0.3);
    border-color: #4CAF50;
}

.episodes-container {
    display: none;
}

.episodes-container.active {
    display: block;
}

.episodes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1rem;
}

/* Responsive */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
    }
    
    .nav-tabs {
        justify-content: center;
    }
    
    .nav-item {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }
    
    .main-content {
        padding: 1rem;
    }
    
    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .categories-grid,
    .movies-grid,
    .series-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
    
    .detail-header {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }
    
    .detail-poster {
        width: 250px;
        height: 375px;
    }
    
    .controls-row {
        flex-wrap: wrap;
        gap: 0.5rem;
    }
    
    .modal-content {
        width: 98%;
        margin: 1% auto;
    }
}

@media (max-width: 480px) {
    .categories-grid,
    .movies-grid,
    .series-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }
    
    .channels-grid {
        grid-template-columns: 1fr;
    }
    
    .modal-body {
        padding: 0;
    }
    
    .detail-content {
        padding: 1rem;
    }
}

/* Estilos para el reproductor simple */
.back-button {
    background: #6c757d;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 5px;
    cursor: pointer;
    margin-bottom: 20px;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    font-size: 14px;
    transition: background-color 0.3s ease;
    grid-column: 1 / -1;
}

.back-button:hover {
    background: #5a6268;
    text-decoration: none;
    color: white;
}

.back-button i {
    margin-right: 5px;
}

.channel-card {
    background: var(--card-bg);
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
}

.channel-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: var(--primary-color);
}

.channel-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto 15px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
}

.channel-card h4 {
    margin: 0 0 10px 0;
    color: var(--text-color);
    font-size: 16px;
    font-weight: 600;
}

.channel-card p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 14px;
}

.loading {
    text-align: center;
    padding: 40px;
    color: var(--text-secondary);
    font-size: 16px;
}

.loading i {
    margin-right: 10px;
    color: var(--primary-color);
}

.error {
    text-align: center;
    padding: 40px;
    color: #e74c3c;
    background: rgba(231, 76, 60, 0.1);
    border-radius: 8px;
    margin: 20px;
    border: 1px solid rgba(231, 76, 60, 0.2);
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
    padding: 20px;
}

.channels-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    padding: 20px;
}

@media (max-width: 768px) {
    .categories-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 15px;
        padding: 15px;
    }
    
    .channels-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 10px;
        padding: 15px;
    }
}
