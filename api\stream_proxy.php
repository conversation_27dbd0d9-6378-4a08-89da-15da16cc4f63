<?php
/**
 * Proxy optimizado para streams HTTPS - VERSIÓN FUNCIONAL
 * Permite servir contenido HTTP a través de HTTPS para evitar problemas de contenido mixto
 * Basado en simple_proxy.php que funciona perfectamente
 */

// Headers básicos
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, HEAD, OPTIONS');
header('Access-Control-Allow-Headers: Range, Content-Type, Accept-Encoding');

// Manejar preflight OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Obtener URL
$url = $_GET['url'] ?? '';

if (empty($url)) {
    http_response_code(400);
    echo 'URL requerida';
    exit();
}

// Log para debugging
error_log("Simple proxy request for: " . $url);

// Verificar que sea del servidor autorizado
$urlHost = parse_url($url, PHP_URL_HOST);
if ($urlHost !== 'rogsworld.uk') {
    http_response_code(403);
    echo 'URL no autorizada: ' . $urlHost;
    exit();
}



// Para HEAD requests, solo verificar que el archivo existe
if ($_SERVER['REQUEST_METHOD'] === 'HEAD') {
    error_log("HEAD request for: " . $url);

    if (function_exists('curl_init')) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_NOBODY, true);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');

        $result = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
        $contentLength = curl_getinfo($ch, CURLINFO_CONTENT_LENGTH_DOWNLOAD);
        $error = curl_error($ch);

        curl_close($ch);

        error_log("HEAD response: HTTP $httpCode, Type: $contentType, Length: $contentLength");

        if ($error) {
            error_log("cURL HEAD error: " . $error);
            http_response_code(500);
            echo 'Error: ' . $error;
            exit();
        }

        if ($httpCode >= 400) {
            http_response_code($httpCode);
            echo 'HTTP Error: ' . $httpCode;
            exit();
        }

        // Configurar headers de respuesta
        http_response_code($httpCode);
        if ($contentType) header('Content-Type: ' . $contentType);
        if ($contentLength > 0) header('Content-Length: ' . $contentLength);
        header('Accept-Ranges: bytes');

        exit();
    }
}

// Para GET requests - Usar streaming optimizado con límites
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    error_log("GET request for: " . $url);

    // Opción 1: Redirect simple (más rápido y confiable)
    if (isset($_GET['redirect']) && $_GET['redirect'] === '1') {
        error_log("Redirecting to: " . $url);
        header('Location: ' . $url);
        exit();
    }

    // Opción 2: Streaming con límites (para casos que requieren proxy)
    if (function_exists('curl_init')) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 120); // Límite de 2 minutos
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
        curl_setopt($ch, CURLOPT_BUFFERSIZE, 16384); // Buffer de 16KB

        // Headers para Range requests
        if (isset($_SERVER['HTTP_RANGE'])) {
            curl_setopt($ch, CURLOPT_HTTPHEADER, ['Range: ' . $_SERVER['HTTP_RANGE']]);
        }

        // Configurar headers de respuesta
        header('Content-Type: video/mp4');
        header('Accept-Ranges: bytes');

        // Limpiar buffer
        if (ob_get_level()) ob_end_clean();

        // Función de escritura con límites para evitar timeouts
        $bytesWritten = 0;
        $maxBytes = 100 * 1024 * 1024; // Límite de 100MB para producción
        $startTime = time();
        $maxTime = 300; // 5 minutos máximo

        curl_setopt($ch, CURLOPT_WRITEFUNCTION, function($ch, $data) use (&$bytesWritten, $maxBytes, $startTime, $maxTime) {
            $len = strlen($data);
            $bytesWritten += $len;
            $elapsed = time() - $startTime;

            // Límites para evitar timeouts
            if ($bytesWritten > $maxBytes) {
                error_log("Reached byte limit: $bytesWritten bytes");
                return 0; // Detener descarga
            }

            if ($elapsed > $maxTime) {
                error_log("Reached time limit: $elapsed seconds");
                return 0; // Detener descarga
            }

            echo $data;
            if (ob_get_level()) ob_flush();
            flush();

            // Verificar si el cliente se desconectó
            if (connection_aborted()) {
                error_log("Client disconnected at $bytesWritten bytes");
                return 0;
            }

            return $len;
        });

        $result = curl_exec($ch);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            error_log("cURL streaming error: " . $error);
        }

        error_log("Streaming completed. Bytes written: $bytesWritten");
        exit();
    } else {
        // Fallback: redirect si cURL no está disponible
        error_log("No cURL available, redirecting to: " . $url);
        header('Location: ' . $url);
        exit();
    }
}

// Si llegamos aquí, método no soportado
http_response_code(405);
echo 'Method not allowed';
?>
