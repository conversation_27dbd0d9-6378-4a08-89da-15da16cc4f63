/* Reset y estilos base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
}

.login-container {
    width: 100%;
    max-width: 400px;
    padding: 2rem;
}

.login-form {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 3rem 2rem;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Logo */
.logo {
    text-align: center;
    margin-bottom: 2rem;
}

.logo i {
    font-size: 3rem;
    color: #4CAF50;
    margin-bottom: 1rem;
    display: block;
}

.logo h1 {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    color: #ffffff;
}

/* Form Groups */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9);
}

.input-group {
    position: relative;
}

.input-group i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: rgba(255, 255, 255, 0.6);
    font-size: 1rem;
}

.input-group input {
    width: 100%;
    padding: 1rem 1rem 1rem 3rem;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.input-group input:focus {
    outline: none;
    border-color: #4CAF50;
    background: rgba(255, 255, 255, 0.15);
}

.input-group input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

/* Login Button */
.login-btn {
    width: 100%;
    padding: 1rem;
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 1rem;
}

.login-btn:hover {
    background: linear-gradient(135deg, #45a049 0%, #3d8b40 100%);
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(76, 175, 80, 0.3);
}

.login-btn:active {
    transform: translateY(0);
}

.login-btn i {
    margin-right: 8px;
}

/* Messages */
.message {
    padding: 1rem;
    border-radius: 8px;
    margin-top: 1rem;
    text-align: center;
    font-weight: 500;
}

.message.error {
    background: rgba(244, 67, 54, 0.2);
    border: 1px solid rgba(244, 67, 54, 0.5);
    color: #ffcdd2;
}

.message.success {
    background: rgba(76, 175, 80, 0.2);
    border: 1px solid rgba(76, 175, 80, 0.5);
    color: #c8e6c9;
}

/* Loading */
.loading {
    text-align: center;
    padding: 2rem 1rem;
    display: none;
}

.loading i {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: #4CAF50;
}

.loading span {
    display: block;
    color: rgba(255, 255, 255, 0.8);
    font-size: 1rem;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.login-form {
    animation: fadeIn 0.6s ease-out;
}

/* Responsive */
@media (max-width: 480px) {
    .login-container {
        padding: 1rem;
    }
    
    .login-form {
        padding: 2rem 1.5rem;
    }
    
    .logo h1 {
        font-size: 1.5rem;
    }
    
    .logo i {
        font-size: 2.5rem;
    }
}

/* Focus states for accessibility */
.input-group input:focus,
.login-btn:focus {
    outline: 2px solid #4CAF50;
    outline-offset: 2px;
}

/* Error state for inputs */
.input-group.error input {
    border-color: #f44336;
    background: rgba(244, 67, 54, 0.1);
}

.input-group.error i {
    color: #f44336;
}

/* Success state for inputs */
.input-group.success input {
    border-color: #4CAF50;
    background: rgba(76, 175, 80, 0.1);
}

.input-group.success i {
    color: #4CAF50;
}
