<?php
require_once __DIR__ . '/config.php';

class XtreamAPI {
    private $server_url;
    private $username;
    private $password;
    private $cache = [];
    
    public function __construct() {
        $this->server_url = XTREAM_SERVER_URL;
    }

    /**
     * Configurar URL del servidor dinámicamente
     */
    public function setServerUrl($url) {
        $this->server_url = rtrim($url, '/');
    }
    
    /**
     * Autenticar usuario con Xtream Codes API
     */
    public function authenticate($username, $password) {
        $this->username = $username;
        $this->password = $password;
        
        $auth_url = $this->server_url . "/player_api.php?username={$username}&password={$password}";
        
        try {
            $response = $this->makeRequest($auth_url);
            
            if ($response && isset($response['user_info']) && $response['user_info']['auth'] == 1) {
                return [
                    'success' => true,
                    'user_info' => $response['user_info'],
                    'server_info' => $response['server_info']
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Credenciales incorrectas o cuenta expirada'
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Error de conexión con el servidor'
            ];
        }
    }
    
    /**
     * Obtener categorías de TV en vivo
     */
    public function getLiveTVCategories() {
        $url = $this->server_url . "/player_api.php?username={$this->username}&password={$this->password}&action=get_live_categories";
        return $this->makeRequest($url);
    }
    
    /**
     * Obtener canales de TV en vivo por categoría
     */
    public function getLiveStreams($category_id = null) {
        $url = $this->server_url . "/player_api.php?username={$this->username}&password={$this->password}&action=get_live_streams";
        if ($category_id) {
            $url .= "&category_id={$category_id}";
        }
        return $this->makeRequest($url);
    }
    
    /**
     * Obtener categorías de películas
     */
    public function getMovieCategories() {
        $url = $this->server_url . "/player_api.php?username={$this->username}&password={$this->password}&action=get_vod_categories";
        return $this->makeRequest($url);
    }
    
    /**
     * Obtener películas por categoría
     */
    public function getMovies($category_id = null) {
        $url = $this->server_url . "/player_api.php?username={$this->username}&password={$this->password}&action=get_vod_streams";
        if ($category_id) {
            $url .= "&category_id={$category_id}";
        }
        return $this->makeRequest($url);
    }
    
    /**
     * Obtener información detallada de una película
     */
    public function getMovieInfo($vod_id) {
        $url = $this->server_url . "/player_api.php?username={$this->username}&password={$this->password}&action=get_vod_info&vod_id={$vod_id}";
        return $this->makeRequest($url);
    }
    
    /**
     * Obtener categorías de series
     */
    public function getSeriesCategories() {
        $url = $this->server_url . "/player_api.php?username={$this->username}&password={$this->password}&action=get_series_categories";
        return $this->makeRequest($url);
    }
    
    /**
     * Obtener series por categoría
     */
    public function getSeries($category_id = null) {
        $url = $this->server_url . "/player_api.php?username={$this->username}&password={$this->password}&action=get_series";
        if ($category_id) {
            $url .= "&category_id={$category_id}";
        }
        return $this->makeRequest($url);
    }
    
    /**
     * Obtener información detallada de una serie
     */
    public function getSeriesInfo($series_id) {
        $url = $this->server_url . "/player_api.php?username={$this->username}&password={$this->password}&action=get_series_info&series_id={$series_id}";
        return $this->makeRequest($url);
    }
    
    /**
     * Generar URL de reproducción para TV en vivo
     */
    public function getLiveStreamUrl($stream_id, $extension = 'ts') {
        return $this->server_url . "/live/{$this->username}/{$this->password}/{$stream_id}.{$extension}";
    }
    
    /**
     * Generar URL de reproducción para película
     */
    public function getMovieStreamUrl($stream_id, $extension = 'mp4') {
        return $this->server_url . "/movie/{$this->username}/{$this->password}/{$stream_id}.{$extension}";
    }
    
    /**
     * Generar URL de reproducción para episodio de serie
     */
    public function getSeriesStreamUrl($stream_id, $extension = 'mp4') {
        return $this->server_url . "/series/{$this->username}/{$this->password}/{$stream_id}.{$extension}";
    }
    
    /**
     * Hacer petición HTTP con cache
     */
    private function makeRequest($url) {
        // Verificar cache si está habilitado
        $cacheKey = md5($url);
        if (CACHE_ENABLED && isset($this->cache[$cacheKey])) {
            $cacheData = $this->cache[$cacheKey];
            if (time() - $cacheData['timestamp'] < CACHE_DURATION) {
                debugLog('Cache hit for: ' . $url);
                return $cacheData['data'];
            }
        }
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, API_TIMEOUT);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_USERAGENT, 'IPTV-WebPlayer/1.0 (Mozilla/5.0 compatible)');
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Accept: application/json',
            'Cache-Control: no-cache'
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        
        if ($error) {
            curl_close($ch);
            logMessage("cURL Error for URL {$url}: {$error}", 'ERROR');
            throw new Exception('Error de conexión: ' . $error);
        }
        
        curl_close($ch);
        
        if ($httpCode !== 200) {
            logMessage("HTTP Error {$httpCode} for URL: {$url}", 'ERROR');
            throw new Exception('Error HTTP: ' . $httpCode);
        }
        
        $decoded = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            logMessage("JSON Error for URL {$url}: " . json_last_error_msg(), 'ERROR');
            throw new Exception('Error al decodificar respuesta del servidor');
        }
        
        // Guardar en cache
        if (CACHE_ENABLED) {
            $this->cache[$cacheKey] = [
                'data' => $decoded,
                'timestamp' => time()
            ];
        }
        
        debugLog('API Response for: ' . $url, 'API');
        
        return $decoded;
    }
    
    /**
     * Establecer credenciales desde sesión
     */
    public function setCredentials($username, $password) {
        $this->username = $username;
        $this->password = $password;
    }
    
    /**
     * Limpiar cache
     */
    public function clearCache() {
        $this->cache = [];
        logMessage('Cache cleared', 'INFO');
    }
    
    /**
     * Obtener estadísticas de cache
     */
    public function getCacheStats() {
        return [
            'entries' => count($this->cache),
            'enabled' => CACHE_ENABLED,
            'duration' => CACHE_DURATION
        ];
    }
}
?>
