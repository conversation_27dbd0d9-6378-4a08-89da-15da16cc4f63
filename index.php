<?php
session_start();
require_once 'includes/config.php';

// Verificar si el usuario está logueado
if (!isset($_SESSION['user_info']) || !isset($_SESSION['server_info'])) {
    header('Location: login.php');
    exit();
}

$user_info = $_SESSION['user_info'];
$server_info = $_SESSION['server_info'];
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IPTV Web Player</title>
    <meta http-equiv="Permissions-Policy" content="autoplay=*, fullscreen=*">
    <!-- CSP manejada dinámicamente por PHP en config.php -->
    <link rel="icon" href="favicon.ico" type="image/x-icon">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/player.css">
    <link rel="stylesheet" href="assets/css/images.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <h1><i class="fas fa-tv"></i> IPTV Player</h1>
                <div class="user-info">
                    <span>Bienvenido, <?php echo htmlspecialchars($user_info['username']); ?></span>
                    <a href="logout.php" class="logout-btn"><i class="fas fa-sign-out-alt"></i> Salir</a>
                </div>
            </div>
        </header>

        <!-- Navigation -->
        <nav class="main-nav">
            <ul class="nav-tabs">
                <li class="nav-item active" data-section="live-tv">
                    <i class="fas fa-broadcast-tower"></i> TV en Vivo
                </li>
                <li class="nav-item" data-section="movies">
                    <i class="fas fa-film"></i> Películas
                </li>
                <li class="nav-item" data-section="series">
                    <i class="fas fa-video"></i> Series
                </li>
            </ul>
        </nav>

        <!-- Content Sections -->
        <main class="main-content">
            <!-- TV en Vivo -->
            <section id="live-tv" class="content-section active">
                <div class="section-header">
                    <h2>Canales de TV</h2>
                </div>
                <div id="live-categories" class="categories-grid">
                    <div class="loading">
                        <i class="fas fa-spinner fa-spin"></i> Cargando canales...
                    </div>
                </div>
                <div id="live-channels" class="channels-grid" style="display: none;">
                    <!-- Los canales se cargarán aquí -->
                </div>
            </section>

            <!-- Películas -->
            <section id="movies" class="content-section">
                <div class="section-header">
                    <h2>Películas</h2>
                    <div class="search-container">
                        <input type="text" id="movies-search" placeholder="Buscar películas...">
                        <i class="fas fa-search"></i>
                    </div>
                </div>
                <div id="movies-categories" class="categories-grid">
                    <div class="loading">
                        <i class="fas fa-spinner fa-spin"></i> Cargando categorías...
                    </div>
                </div>
                <div id="movies-list" class="movies-grid" style="display: none;">
                    <!-- Las películas se cargarán aquí -->
                </div>
            </section>

            <!-- Series -->
            <section id="series" class="content-section">
                <div class="section-header">
                    <h2>Series</h2>
                    <div class="search-container">
                        <input type="text" id="series-search" placeholder="Buscar series...">
                        <i class="fas fa-search"></i>
                    </div>
                </div>
                <div id="series-categories" class="categories-grid">
                    <div class="loading">
                        <i class="fas fa-spinner fa-spin"></i> Cargando categorías...
                    </div>
                </div>
                <div id="series-list" class="series-grid" style="display: none;">
                    <!-- Las series se cargarán aquí -->
                </div>
            </section>
        </main>
    </div>

    <!-- Video Player Modal -->
    <div id="video-player-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="player-title">Reproductor</h3>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <div class="video-container">
                    <video id="main-video" controls preload="metadata" crossorigin="anonymous" playsinline>
                        <source id="video-source" src="" type="application/x-mpegURL">
                        Tu navegador no soporta el elemento de video.
                    </video>
                    
                    <!-- Custom Controls -->
                    <div class="custom-controls">
                        <div class="controls-row">
                            <button id="play-pause-btn"><i class="fas fa-play"></i></button>
                            <button id="backward-btn"><i class="fas fa-backward"></i></button>
                            <button id="forward-btn"><i class="fas fa-forward"></i></button>
                            <button id="next-episode-btn" style="display: none;"><i class="fas fa-step-forward"></i></button>
                            
                            <div class="volume-container">
                                <button id="volume-btn"><i class="fas fa-volume-up"></i></button>
                                <input type="range" id="volume-slider" min="0" max="100" value="100">
                            </div>
                            
                            <div class="time-display">
                                <span id="current-time">00:00</span> / <span id="duration">00:00</span>
                            </div>
                            
                            <div class="right-controls">
                                <button id="subtitles-btn"><i class="fas fa-closed-captioning"></i></button>
                                <button id="language-btn"><i class="fas fa-language"></i></button>
                                <button id="fullscreen-btn"><i class="fas fa-expand"></i></button>
                            </div>
                        </div>
                        
                        <div class="progress-container">
                            <input type="range" id="progress-slider" min="0" max="100" value="0">
                        </div>
                    </div>
                </div>
                
                <!-- Episode List for Series -->
                <div id="episode-list" class="episode-list" style="display: none;">
                    <!-- Los episodios se cargarán aquí -->
                </div>
            </div>
        </div>
    </div>

    <!-- Series Detail Modal -->
    <div id="series-detail-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="series-detail-title">Información de la Serie</h3>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <div id="series-detail-content">
                    <!-- El contenido de la serie se cargará aquí -->
                </div>
            </div>
        </div>
    </div>

    <!-- Movie Detail Modal -->
    <div id="movie-detail-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="movie-detail-title">Información de la Película</h3>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <div id="movie-detail-content">
                    <!-- El contenido de la película se cargará aquí -->
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
    <script src="assets/js/app-simple.js"></script>

</body>
</html>
