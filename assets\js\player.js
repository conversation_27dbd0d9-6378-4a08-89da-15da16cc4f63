class VideoPlayer {
    constructor(videoElement) {
        this.video = videoElement;
        this.hls = null;
        this.subtitles = [];
        this.currentSubtitle = -1;
        this.audioTracks = [];
        this.currentAudioTrack = 0;
        
        this.initializePlayer();
    }

    initializePlayer() {
        this.setupEventListeners();
        this.createCustomControls();
        this.setupKeyboardShortcuts();
    }

    setupEventListeners() {
        // Video events
        this.video.addEventListener('loadstart', () => this.onLoadStart());
        this.video.addEventListener('loadedmetadata', () => this.onLoadedMetadata());
        this.video.addEventListener('timeupdate', () => this.onTimeUpdate());
        this.video.addEventListener('ended', () => this.onEnded());
        this.video.addEventListener('error', (e) => this.onError(e));
        this.video.addEventListener('play', () => this.onPlay());
        this.video.addEventListener('pause', () => this.onPause());
        this.video.addEventListener('waiting', () => this.onWaiting());
        this.video.addEventListener('canplay', () => this.onCanPlay());
        
        // Fullscreen events
        document.addEventListener('fullscreenchange', () => this.onFullscreenChange());
        document.addEventListener('webkitfullscreenchange', () => this.onFullscreenChange());
        document.addEventListener('mozfullscreenchange', () => this.onFullscreenChange());
        document.addEventListener('MSFullscreenChange', () => this.onFullscreenChange());
    }

    createCustomControls() {
        const container = this.video.parentElement;
        
        // Create controls overlay
        const controlsOverlay = document.createElement('div');
        controlsOverlay.className = 'video-controls-overlay';
        controlsOverlay.innerHTML = this.getControlsHTML();
        
        container.appendChild(controlsOverlay);
        
        // Setup control events
        this.setupControlEvents(controlsOverlay);
        
        // Auto-hide controls
        this.setupAutoHideControls(container, controlsOverlay);
    }

    getControlsHTML() {
        return `
            <div class="video-controls">
                <!-- Loading spinner -->
                <div class="video-loading" style="display: none;">
                    <div class="loading-spinner">
                        <i class="fas fa-spinner fa-spin"></i>
                    </div>
                </div>
                
                <!-- Play/Pause overlay -->
                <div class="play-pause-overlay">
                    <button class="play-pause-center">
                        <i class="fas fa-play"></i>
                    </button>
                </div>
                
                <!-- Bottom controls -->
                <div class="bottom-controls">
                    <!-- Progress bar -->
                    <div class="progress-container">
                        <div class="progress-bar">
                            <div class="progress-buffer"></div>
                            <div class="progress-played"></div>
                            <div class="progress-handle"></div>
                        </div>
                        <div class="time-tooltip">00:00</div>
                    </div>
                    
                    <!-- Control buttons -->
                    <div class="control-buttons">
                        <div class="left-controls">
                            <button class="btn-play-pause">
                                <i class="fas fa-play"></i>
                            </button>
                            <button class="btn-backward">
                                <i class="fas fa-backward"></i>
                                <span class="btn-label">-10s</span>
                            </button>
                            <button class="btn-forward">
                                <i class="fas fa-forward"></i>
                                <span class="btn-label">+10s</span>
                            </button>
                            <button class="btn-next-episode" style="display: none;">
                                <i class="fas fa-step-forward"></i>
                                <span class="btn-label">Siguiente</span>
                            </button>
                            
                            <!-- Volume control -->
                            <div class="volume-control">
                                <button class="btn-volume">
                                    <i class="fas fa-volume-up"></i>
                                </button>
                                <div class="volume-slider-container">
                                    <div class="volume-slider">
                                        <div class="volume-track">
                                            <div class="volume-fill"></div>
                                            <div class="volume-handle"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Time display -->
                            <div class="time-display">
                                <span class="current-time">00:00</span>
                                <span class="separator">/</span>
                                <span class="total-time">00:00</span>
                            </div>
                        </div>
                        
                        <div class="right-controls">
                            <!-- Subtitles -->
                            <div class="btn-group">
                                <button class="btn-subtitles">
                                    <i class="fas fa-closed-captioning"></i>
                                    <span class="btn-label">Subtítulos</span>
                                </button>
                                <div class="dropdown-menu subtitles-menu">
                                    <div class="menu-item" data-subtitle="-1">Sin subtítulos</div>
                                </div>
                            </div>
                            
                            <!-- Audio tracks -->
                            <div class="btn-group">
                                <button class="btn-audio">
                                    <i class="fas fa-language"></i>
                                    <span class="btn-label">Audio</span>
                                </button>
                                <div class="dropdown-menu audio-menu">
                                    <div class="menu-item active" data-audio="0">Audio por defecto</div>
                                </div>
                            </div>
                            
                            <!-- Quality -->
                            <div class="btn-group">
                                <button class="btn-quality">
                                    <i class="fas fa-cog"></i>
                                    <span class="btn-label">Auto</span>
                                </button>
                                <div class="dropdown-menu quality-menu">
                                    <div class="menu-item active" data-quality="auto">Automática</div>
                                </div>
                            </div>
                            
                            <!-- Picture in Picture -->
                            <button class="btn-pip" style="display: none;">
                                <i class="fas fa-external-link-alt"></i>
                                <span class="btn-label">PiP</span>
                            </button>
                            
                            <!-- Fullscreen -->
                            <button class="btn-fullscreen">
                                <i class="fas fa-expand"></i>
                                <span class="btn-label">Pantalla completa</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    setupControlEvents(controlsOverlay) {
        // Play/Pause
        const playPauseBtn = controlsOverlay.querySelector('.btn-play-pause');
        const playPauseCenter = controlsOverlay.querySelector('.play-pause-center');
        
        [playPauseBtn, playPauseCenter].forEach(btn => {
            btn.addEventListener('click', () => this.togglePlayPause());
        });

        // Seek buttons
        controlsOverlay.querySelector('.btn-backward').addEventListener('click', () => this.seek(-10));
        controlsOverlay.querySelector('.btn-forward').addEventListener('click', () => this.seek(10));

        // Volume
        const volumeBtn = controlsOverlay.querySelector('.btn-volume');
        const volumeSlider = controlsOverlay.querySelector('.volume-slider');
        
        volumeBtn.addEventListener('click', () => this.toggleMute());
        this.setupVolumeSlider(volumeSlider);

        // Progress bar
        const progressBar = controlsOverlay.querySelector('.progress-bar');
        this.setupProgressBar(progressBar);

        // Fullscreen
        controlsOverlay.querySelector('.btn-fullscreen').addEventListener('click', () => this.toggleFullscreen());

        // Subtitles
        controlsOverlay.querySelector('.btn-subtitles').addEventListener('click', (e) => {
            this.toggleDropdown(e.target.closest('.btn-group'));
        });

        // Audio tracks
        controlsOverlay.querySelector('.btn-audio').addEventListener('click', (e) => {
            this.toggleDropdown(e.target.closest('.btn-group'));
        });

        // Quality
        controlsOverlay.querySelector('.btn-quality').addEventListener('click', (e) => {
            this.toggleDropdown(e.target.closest('.btn-group'));
        });

        // Picture in Picture
        if ('pictureInPictureEnabled' in document) {
            const pipBtn = controlsOverlay.querySelector('.btn-pip');
            pipBtn.style.display = 'block';
            pipBtn.addEventListener('click', () => this.togglePictureInPicture());
        }

        // Close dropdowns when clicking outside
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.btn-group')) {
                this.closeAllDropdowns();
            }
        });
    }

    setupAutoHideControls(container, controlsOverlay) {
        let hideTimeout;
        
        const showControls = () => {
            controlsOverlay.classList.add('show');
            clearTimeout(hideTimeout);
            
            if (!this.video.paused) {
                hideTimeout = setTimeout(() => {
                    controlsOverlay.classList.remove('show');
                }, 3000);
            }
        };

        const hideControls = () => {
            if (!this.video.paused) {
                controlsOverlay.classList.remove('show');
            }
        };

        // Show controls on mouse move/touch
        container.addEventListener('mousemove', showControls);
        container.addEventListener('touchstart', showControls);
        container.addEventListener('mouseenter', showControls);
        container.addEventListener('mouseleave', hideControls);

        // Always show controls when paused
        this.video.addEventListener('pause', showControls);
        this.video.addEventListener('play', () => {
            hideTimeout = setTimeout(() => {
                controlsOverlay.classList.remove('show');
            }, 3000);
        });

        // Initial show
        showControls();
    }

    setupVolumeSlider(volumeSlider) {
        const track = volumeSlider.querySelector('.volume-track');
        const fill = volumeSlider.querySelector('.volume-fill');
        const handle = volumeSlider.querySelector('.volume-handle');
        
        let isDragging = false;

        const updateVolume = (e) => {
            const rect = track.getBoundingClientRect();
            const percent = Math.max(0, Math.min(1, (e.clientX - rect.left) / rect.width));
            
            this.video.volume = percent;
            this.updateVolumeDisplay(percent);
        };

        track.addEventListener('mousedown', (e) => {
            isDragging = true;
            updateVolume(e);
        });

        document.addEventListener('mousemove', (e) => {
            if (isDragging) {
                updateVolume(e);
            }
        });

        document.addEventListener('mouseup', () => {
            isDragging = false;
        });

        // Touch events
        track.addEventListener('touchstart', (e) => {
            e.preventDefault();
            isDragging = true;
            updateVolume(e.touches[0]);
        });

        document.addEventListener('touchmove', (e) => {
            if (isDragging) {
                e.preventDefault();
                updateVolume(e.touches[0]);
            }
        });

        document.addEventListener('touchend', () => {
            isDragging = false;
        });
    }

    setupProgressBar(progressBar) {
        const tooltip = progressBar.parentElement.querySelector('.time-tooltip');
        let isDragging = false;

        const updateProgress = (e) => {
            const rect = progressBar.getBoundingClientRect();
            const percent = Math.max(0, Math.min(1, (e.clientX - rect.left) / rect.width));
            const time = percent * this.video.duration;
            
            this.video.currentTime = time;
        };

        const showTooltip = (e) => {
            const rect = progressBar.getBoundingClientRect();
            const percent = Math.max(0, Math.min(1, (e.clientX - rect.left) / rect.width));
            const time = percent * this.video.duration;
            
            tooltip.textContent = this.formatTime(time);
            tooltip.style.left = `${e.clientX - rect.left}px`;
            tooltip.style.display = 'block';
        };

        progressBar.addEventListener('mousedown', (e) => {
            isDragging = true;
            updateProgress(e);
        });

        progressBar.addEventListener('mousemove', (e) => {
            if (this.video.duration) {
                showTooltip(e);
            }
        });

        progressBar.addEventListener('mouseleave', () => {
            tooltip.style.display = 'none';
        });

        document.addEventListener('mousemove', (e) => {
            if (isDragging) {
                updateProgress(e);
            }
        });

        document.addEventListener('mouseup', () => {
            isDragging = false;
        });

        // Touch events
        progressBar.addEventListener('touchstart', (e) => {
            e.preventDefault();
            isDragging = true;
            updateProgress(e.touches[0]);
        });

        document.addEventListener('touchmove', (e) => {
            if (isDragging) {
                e.preventDefault();
                updateProgress(e.touches[0]);
            }
        });

        document.addEventListener('touchend', () => {
            isDragging = false;
        });
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Only handle shortcuts when video player is active
            if (document.activeElement === this.video || this.video.parentElement.contains(document.activeElement)) {
                this.handleKeyboard(e);
            }
        });
    }

    handleKeyboard(e) {
        switch(e.key) {
            case ' ':
            case 'k':
                e.preventDefault();
                this.togglePlayPause();
                break;
            case 'ArrowLeft':
                e.preventDefault();
                this.seek(-10);
                break;
            case 'ArrowRight':
                e.preventDefault();
                this.seek(10);
                break;
            case 'ArrowUp':
                e.preventDefault();
                this.changeVolume(0.1);
                break;
            case 'ArrowDown':
                e.preventDefault();
                this.changeVolume(-0.1);
                break;
            case 'f':
                e.preventDefault();
                this.toggleFullscreen();
                break;
            case 'm':
                e.preventDefault();
                this.toggleMute();
                break;
            case 'j':
                e.preventDefault();
                this.seek(-10);
                break;
            case 'l':
                e.preventDefault();
                this.seek(10);
                break;
            case '0':
            case '1':
            case '2':
            case '3':
            case '4':
            case '5':
            case '6':
            case '7':
            case '8':
            case '9':
                e.preventDefault();
                const percent = parseInt(e.key) / 10;
                this.video.currentTime = this.video.duration * percent;
                break;
        }
    }

    // Player control methods
    togglePlayPause() {
        if (this.video.paused) {
            this.play();
        } else {
            this.pause();
        }
    }

    play() {
        this.video.play().catch(e => {
            console.error('Error playing video:', e);
        });
    }

    pause() {
        this.video.pause();
    }

    seek(seconds) {
        this.video.currentTime = Math.max(0, Math.min(this.video.duration, this.video.currentTime + seconds));
    }

    changeVolume(delta) {
        this.video.volume = Math.max(0, Math.min(1, this.video.volume + delta));
        this.updateVolumeDisplay(this.video.volume);
    }

    toggleMute() {
        this.video.muted = !this.video.muted;
        this.updateVolumeIcon();
    }

    toggleFullscreen() {
        const container = this.video.parentElement;
        
        if (document.fullscreenElement) {
            document.exitFullscreen();
        } else {
            container.requestFullscreen().catch(e => {
                console.error('Error entering fullscreen:', e);
            });
        }
    }

    togglePictureInPicture() {
        if (document.pictureInPictureElement) {
            document.exitPictureInPicture();
        } else {
            this.video.requestPictureInPicture().catch(e => {
                console.error('Error entering PiP:', e);
            });
        }
    }

    // HLS setup
    setupHLS(url) {
        if (this.hls) {
            this.hls.destroy();
        }

        if (Hls.isSupported()) {
            this.hls = new Hls({
                enableWorker: true,
                lowLatencyMode: true,
                backBufferLength: 90,
                maxBufferLength: 30,
                maxMaxBufferLength: 600,
                startLevel: -1,
                capLevelToPlayerSize: true,
                xhrSetup: function(xhr, url) {
                    // Configuración para CORS y mixed content
                    xhr.withCredentials = false;
                    xhr.setRequestHeader('Accept', '*/*');
                    xhr.setRequestHeader('Cache-Control', 'no-cache');
                    xhr.timeout = 30000;
                },
                manifestLoadingTimeOut: 30000,
                manifestLoadingMaxRetry: 3,
                manifestLoadingRetryDelay: 1000,
                levelLoadingTimeOut: 30000,
                levelLoadingMaxRetry: 3,
                levelLoadingRetryDelay: 1000,
                fragLoadingTimeOut: 30000,
                fragLoadingMaxRetry: 3,
                fragLoadingRetryDelay: 1000
            });

            this.hls.loadSource(url);
            this.hls.attachMedia(this.video);

            this.hls.on(Hls.Events.MANIFEST_PARSED, () => {
                this.onHLSManifestParsed();
            });

            this.hls.on(Hls.Events.LEVEL_LOADED, (event, data) => {
                this.updateQualityMenu(this.hls.levels);
            });

            this.hls.on(Hls.Events.ERROR, (event, data) => {
                this.onHLSError(data);
            });

            this.hls.on(Hls.Events.SUBTITLE_TRACKS_UPDATED, (event, data) => {
                this.updateSubtitleTracks(data.subtitleTracks);
            });

            this.hls.on(Hls.Events.AUDIO_TRACKS_UPDATED, (event, data) => {
                this.updateAudioTracks(data.audioTracks);
            });

        } else if (this.video.canPlayType('application/vnd.apple.mpegurl')) {
            // Safari native HLS support
            this.video.src = url;
        } else {
            console.error('HLS not supported');
        }
    }

    // Event handlers
    onLoadStart() {
        this.showLoading(true);
    }

    onLoadedMetadata() {
        this.updateTimeDisplay();
        this.showLoading(false);
    }

    onTimeUpdate() {
        this.updateProgressBar();
        this.updateTimeDisplay();
    }

    onEnded() {
        this.updatePlayPauseIcon();
        // Trigger next episode if available
        if (this.onVideoEnded) {
            this.onVideoEnded();
        }
    }

    onError(e) {
        console.error('Video error:', e);
        this.showLoading(false);
        this.showError('Error al reproducir el video');
    }

    onPlay() {
        this.updatePlayPauseIcon();
    }

    onPause() {
        this.updatePlayPauseIcon();
    }

    onWaiting() {
        this.showLoading(true);
    }

    onCanPlay() {
        this.showLoading(false);
    }

    onFullscreenChange() {
        this.updateFullscreenIcon();
    }

    onHLSManifestParsed() {
        this.video.play().catch(e => {
            console.log('Autoplay prevented:', e);
        });
    }

    onHLSError(data) {
        console.error('HLS Error:', data);

        if (data.fatal) {
            switch(data.type) {
                case Hls.ErrorTypes.NETWORK_ERROR:
                    console.log('Network error, trying to recover...');
                    // Intentar reiniciar después de un breve delay
                    setTimeout(() => {
                        if (this.hls) {
                            this.hls.startLoad();
                        }
                    }, 1000);
                    break;
                case Hls.ErrorTypes.MEDIA_ERROR:
                    console.log('Media error, trying to recover...');
                    // Intentar recuperar del error de media
                    setTimeout(() => {
                        if (this.hls) {
                            this.hls.recoverMediaError();
                        }
                    }, 1000);
                    break;
                default:
                    console.log('Fatal error, destroying HLS...');
                    this.hls.destroy();
                    this.showError('Error fatal en la reproducción. Intenta recargar el video.');
                    break;
            }
        } else {
            // Errores no fatales, solo log
            console.log('Non-fatal HLS error:', data.details);
        }
    }

    // UI update methods
    updatePlayPauseIcon() {
        const buttons = document.querySelectorAll('.btn-play-pause, .play-pause-center');
        const icon = this.video.paused ? 'fa-play' : 'fa-pause';
        
        buttons.forEach(btn => {
            const i = btn.querySelector('i');
            if (i) {
                i.className = `fas ${icon}`;
            }
        });
    }

    updateVolumeIcon() {
        const btn = document.querySelector('.btn-volume i');
        if (!btn) return;
        
        if (this.video.muted || this.video.volume === 0) {
            btn.className = 'fas fa-volume-mute';
        } else if (this.video.volume < 0.5) {
            btn.className = 'fas fa-volume-down';
        } else {
            btn.className = 'fas fa-volume-up';
        }
    }

    updateVolumeDisplay(volume) {
        const fill = document.querySelector('.volume-fill');
        const handle = document.querySelector('.volume-handle');
        
        if (fill && handle) {
            const percent = volume * 100;
            fill.style.width = `${percent}%`;
            handle.style.left = `${percent}%`;
        }
        
        this.updateVolumeIcon();
    }

    updateProgressBar() {
        if (!this.video.duration) return;
        
        const played = document.querySelector('.progress-played');
        const handle = document.querySelector('.progress-handle');
        
        if (played && handle) {
            const percent = (this.video.currentTime / this.video.duration) * 100;
            played.style.width = `${percent}%`;
            handle.style.left = `${percent}%`;
        }
        
        // Update buffer
        const buffer = document.querySelector('.progress-buffer');
        if (buffer && this.video.buffered.length > 0) {
            const bufferedEnd = this.video.buffered.end(this.video.buffered.length - 1);
            const bufferedPercent = (bufferedEnd / this.video.duration) * 100;
            buffer.style.width = `${bufferedPercent}%`;
        }
    }

    updateTimeDisplay() {
        const currentTime = document.querySelector('.current-time');
        const totalTime = document.querySelector('.total-time');
        
        if (currentTime) {
            currentTime.textContent = this.formatTime(this.video.currentTime);
        }
        
        if (totalTime && this.video.duration) {
            totalTime.textContent = this.formatTime(this.video.duration);
        }
    }

    updateFullscreenIcon() {
        const btn = document.querySelector('.btn-fullscreen i');
        if (!btn) return;
        
        if (document.fullscreenElement) {
            btn.className = 'fas fa-compress';
        } else {
            btn.className = 'fas fa-expand';
        }
    }

    updateQualityMenu(levels) {
        const menu = document.querySelector('.quality-menu');
        if (!menu || !levels) return;
        
        menu.innerHTML = '<div class="menu-item active" data-quality="auto">Automática</div>';
        
        levels.forEach((level, index) => {
            const item = document.createElement('div');
            item.className = 'menu-item';
            item.dataset.quality = index;
            item.textContent = `${level.height}p`;
            
            item.addEventListener('click', () => {
                this.setQuality(index);
                this.closeAllDropdowns();
            });
            
            menu.appendChild(item);
        });
    }

    updateSubtitleTracks(tracks) {
        const menu = document.querySelector('.subtitles-menu');
        if (!menu) return;
        
        this.subtitles = tracks;
        menu.innerHTML = '<div class="menu-item active" data-subtitle="-1">Sin subtítulos</div>';
        
        tracks.forEach((track, index) => {
            const item = document.createElement('div');
            item.className = 'menu-item';
            item.dataset.subtitle = index;
            item.textContent = track.name || `Subtítulo ${index + 1}`;
            
            item.addEventListener('click', () => {
                this.setSubtitle(index);
                this.closeAllDropdowns();
            });
            
            menu.appendChild(item);
        });
    }

    updateAudioTracks(tracks) {
        const menu = document.querySelector('.audio-menu');
        if (!menu) return;
        
        this.audioTracks = tracks;
        menu.innerHTML = '';
        
        tracks.forEach((track, index) => {
            const item = document.createElement('div');
            item.className = index === 0 ? 'menu-item active' : 'menu-item';
            item.dataset.audio = index;
            item.textContent = track.name || `Audio ${index + 1}`;
            
            item.addEventListener('click', () => {
                this.setAudioTrack(index);
                this.closeAllDropdowns();
            });
            
            menu.appendChild(item);
        });
    }

    // Control methods
    setQuality(levelIndex) {
        if (this.hls) {
            this.hls.currentLevel = levelIndex;
            
            // Update UI
            const menuItems = document.querySelectorAll('.quality-menu .menu-item');
            menuItems.forEach(item => item.classList.remove('active'));
            
            const selectedItem = document.querySelector(`[data-quality="${levelIndex}"]`);
            if (selectedItem) {
                selectedItem.classList.add('active');
                
                const btn = document.querySelector('.btn-quality .btn-label');
                if (btn) {
                    btn.textContent = levelIndex === -1 ? 'Auto' : selectedItem.textContent;
                }
            }
        }
    }

    setSubtitle(trackIndex) {
        if (this.hls) {
            this.hls.subtitleTrack = trackIndex;
            this.currentSubtitle = trackIndex;
            
            // Update UI
            const menuItems = document.querySelectorAll('.subtitles-menu .menu-item');
            menuItems.forEach(item => item.classList.remove('active'));
            
            const selectedItem = document.querySelector(`[data-subtitle="${trackIndex}"]`);
            if (selectedItem) {
                selectedItem.classList.add('active');
            }
        }
    }

    setAudioTrack(trackIndex) {
        if (this.hls) {
            this.hls.audioTrack = trackIndex;
            this.currentAudioTrack = trackIndex;
            
            // Update UI
            const menuItems = document.querySelectorAll('.audio-menu .menu-item');
            menuItems.forEach(item => item.classList.remove('active'));
            
            const selectedItem = document.querySelector(`[data-audio="${trackIndex}"]`);
            if (selectedItem) {
                selectedItem.classList.add('active');
            }
        }
    }

    toggleDropdown(btnGroup) {
        const isOpen = btnGroup.classList.contains('open');
        
        // Close all dropdowns first
        this.closeAllDropdowns();
        
        // Toggle current dropdown
        if (!isOpen) {
            btnGroup.classList.add('open');
        }
    }

    closeAllDropdowns() {
        document.querySelectorAll('.btn-group').forEach(group => {
            group.classList.remove('open');
        });
    }

    showLoading(show) {
        const loading = document.querySelector('.video-loading');
        if (loading) {
            loading.style.display = show ? 'flex' : 'none';
        }
    }

    showError(message) {
        console.error(message);
        // Could implement a toast notification here
    }

    formatTime(seconds) {
        if (isNaN(seconds)) return '00:00';
        
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);
        
        if (hours > 0) {
            return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        } else {
            return `${minutes}:${secs.toString().padStart(2, '0')}`;
        }
    }

    destroy() {
        if (this.hls) {
            this.hls.destroy();
            this.hls = null;
        }
        
        // Remove custom controls
        const controlsOverlay = this.video.parentElement.querySelector('.video-controls-overlay');
        if (controlsOverlay) {
            controlsOverlay.remove();
        }
    }
}

// Export for use in other modules
window.VideoPlayer = VideoPlayer;
