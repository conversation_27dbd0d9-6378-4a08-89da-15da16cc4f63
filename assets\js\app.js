class IPTVApp {
    constructor() {
        this.currentSection = 'live-tv';
        this.currentCategory = null;
        this.currentVideo = null;
        this.currentEpisodes = [];
        this.currentEpisodeIndex = 0;
        
        this.initializeApp();
    }

    initializeApp() {
        this.setupEventListeners();
        this.loadSection('live-tv');
    }

    setupEventListeners() {
        // Navigation tabs
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', (e) => {
                const section = e.currentTarget.dataset.section;
                this.switchSection(section);
            });
        });

        // Close modals
        document.querySelectorAll('.close-modal').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.closeModal(e.target.closest('.modal'));
            });
        });

        // Close modal clicking outside
        document.querySelectorAll('.modal').forEach(modal => {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    this.closeModal(modal);
                }
            });
        });

        // Search functionality
        this.setupSearch();

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });
    }

    setupSearch() {
        const moviesSearch = document.getElementById('movies-search');
        const seriesSearch = document.getElementById('series-search');

        if (moviesSearch) {
            let searchTimeout;
            moviesSearch.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    this.searchContent(e.target.value, 'movies');
                }, 500);
            });
        }

        if (seriesSearch) {
            let searchTimeout;
            seriesSearch.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    this.searchContent(e.target.value, 'series');
                }, 500);
            });
        }
    }

    switchSection(section) {
        // Update navigation
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-section="${section}"]`).classList.add('active');

        // Update content
        document.querySelectorAll('.content-section').forEach(section => {
            section.classList.remove('active');
        });
        document.getElementById(section).classList.add('active');

        this.currentSection = section;
        this.loadSection(section);
    }

    async loadSection(section) {
        try {
            switch (section) {
                case 'live-tv':
                    await this.loadLiveTV();
                    break;
                case 'movies':
                    await this.loadMovies();
                    break;
                case 'series':
                    await this.loadSeries();
                    break;
            }
        } catch (error) {
            console.error('Error loading section:', error);
            this.showError('Error al cargar el contenido');
        }
    }

    async loadLiveTV() {
        const categoriesContainer = document.getElementById('live-categories');
        
        try {
            const response = await fetch('api/xtream.php?action=get_live_categories');
            const categories = await response.json();

            if (categories.error) {
                throw new Error(categories.error);
            }

            categoriesContainer.innerHTML = this.renderCategories(categories, 'live');
            
            // Add click events to categories
            categoriesContainer.querySelectorAll('.category-card').forEach(card => {
                card.addEventListener('click', () => {
                    const categoryId = card.dataset.categoryId;
                    this.loadLiveChannels(categoryId);
                });
            });

        } catch (error) {
            categoriesContainer.innerHTML = `<div class="error">Error al cargar categorías: ${error.message}</div>`;
        }
    }

    async loadLiveChannels(categoryId) {
        const channelsContainer = document.getElementById('live-channels');
        const categoriesContainer = document.getElementById('live-categories');
        
        channelsContainer.innerHTML = '<div class="loading"><i class="fas fa-spinner fa-spin"></i> Cargando canales...</div>';
        channelsContainer.style.display = 'block';
        categoriesContainer.style.display = 'none';

        try {
            const response = await fetch(`api/xtream.php?action=get_live_streams&category_id=${categoryId}`);
            const channels = await response.json();

            if (channels.error) {
                throw new Error(channels.error);
            }

            channelsContainer.innerHTML = this.renderChannels(channels);
            
            // Add click events to channels
            channelsContainer.querySelectorAll('.channel-card').forEach(card => {
                card.addEventListener('click', () => {
                    const streamId = card.dataset.streamId;
                    const channelName = card.dataset.channelName;
                    this.playLiveChannel(streamId, channelName);
                });
            });

            // Add back button
            const backBtn = document.createElement('button');
            backBtn.innerHTML = '<i class="fas fa-arrow-left"></i> Volver a categorías';
            backBtn.className = 'back-btn';
            backBtn.style.cssText = 'margin-bottom: 1rem; padding: 0.5rem 1rem; background: #4CAF50; color: white; border: none; border-radius: 5px; cursor: pointer;';
            backBtn.addEventListener('click', () => {
                categoriesContainer.style.display = 'grid';
                channelsContainer.style.display = 'none';
            });
            channelsContainer.insertBefore(backBtn, channelsContainer.firstChild);

        } catch (error) {
            channelsContainer.innerHTML = `<div class="error">Error al cargar canales: ${error.message}</div>`;
        }
    }

    async loadMovies() {
        const categoriesContainer = document.getElementById('movies-categories');
        
        try {
            const response = await fetch('api/xtream.php?action=get_movie_categories');
            const categories = await response.json();

            if (categories.error) {
                throw new Error(categories.error);
            }

            categoriesContainer.innerHTML = this.renderCategories(categories, 'movies');
            
            // Add click events to categories
            categoriesContainer.querySelectorAll('.category-card').forEach(card => {
                card.addEventListener('click', () => {
                    const categoryId = card.dataset.categoryId;
                    this.loadMoviesList(categoryId);
                });
            });

        } catch (error) {
            categoriesContainer.innerHTML = `<div class="error">Error al cargar categorías: ${error.message}</div>`;
        }
    }

    async loadMoviesList(categoryId) {
        const moviesContainer = document.getElementById('movies-list');
        const categoriesContainer = document.getElementById('movies-categories');
        
        moviesContainer.innerHTML = '<div class="loading"><i class="fas fa-spinner fa-spin"></i> Cargando películas...</div>';
        moviesContainer.style.display = 'grid';
        categoriesContainer.style.display = 'none';

        try {
            const response = await fetch(`api/xtream.php?action=get_movies&category_id=${categoryId}`);
            const movies = await response.json();

            if (movies.error) {
                throw new Error(movies.error);
            }

            moviesContainer.innerHTML = this.renderMovies(movies);
            
            // Add click events to movies
            moviesContainer.querySelectorAll('.movie-card').forEach(card => {
                card.addEventListener('click', () => {
                    const movieId = card.dataset.movieId;
                    this.showMovieDetail(movieId);
                });
            });

            // Add back button
            this.addBackButton(moviesContainer, () => {
                categoriesContainer.style.display = 'grid';
                moviesContainer.style.display = 'none';
            });

        } catch (error) {
            moviesContainer.innerHTML = `<div class="error">Error al cargar películas: ${error.message}</div>`;
        }
    }

    async loadSeries() {
        const categoriesContainer = document.getElementById('series-categories');
        
        try {
            const response = await fetch('api/xtream.php?action=get_series_categories');
            const categories = await response.json();

            if (categories.error) {
                throw new Error(categories.error);
            }

            categoriesContainer.innerHTML = this.renderCategories(categories, 'series');
            
            // Add click events to categories
            categoriesContainer.querySelectorAll('.category-card').forEach(card => {
                card.addEventListener('click', () => {
                    const categoryId = card.dataset.categoryId;
                    this.loadSeriesList(categoryId);
                });
            });

        } catch (error) {
            categoriesContainer.innerHTML = `<div class="error">Error al cargar categorías: ${error.message}</div>`;
        }
    }

    async loadSeriesList(categoryId) {
        const seriesContainer = document.getElementById('series-list');
        const categoriesContainer = document.getElementById('series-categories');
        
        seriesContainer.innerHTML = '<div class="loading"><i class="fas fa-spinner fa-spin"></i> Cargando series...</div>';
        seriesContainer.style.display = 'grid';
        categoriesContainer.style.display = 'none';

        try {
            const response = await fetch(`api/xtream.php?action=get_series&category_id=${categoryId}`);
            const series = await response.json();

            if (series.error) {
                throw new Error(series.error);
            }

            seriesContainer.innerHTML = this.renderSeries(series);
            
            // Add click events to series
            seriesContainer.querySelectorAll('.series-card').forEach(card => {
                card.addEventListener('click', () => {
                    const seriesId = card.dataset.seriesId;
                    this.showSeriesDetail(seriesId);
                });
            });

            // Add back button
            this.addBackButton(seriesContainer, () => {
                categoriesContainer.style.display = 'grid';
                seriesContainer.style.display = 'none';
            });

        } catch (error) {
            seriesContainer.innerHTML = `<div class="error">Error al cargar series: ${error.message}</div>`;
        }
    }

    renderCategories(categories, type) {
        if (!Array.isArray(categories)) return '<div class="error">No se pudieron cargar las categorías</div>';
        
        return categories.map(category => `
            <div class="category-card" data-category-id="${category.category_id}">
                <h3>${this.escapeHtml(category.category_name)}</h3>
                <div class="channel-count">${category.count || 0} elementos</div>
            </div>
        `).join('');
    }

    renderChannels(channels) {
        if (!Array.isArray(channels)) return '<div class="error">No se pudieron cargar los canales</div>';
        
        return channels.map(channel => `
            <div class="channel-card" data-stream-id="${channel.stream_id}" data-channel-name="${this.escapeHtml(channel.name)}">
                <img class="channel-logo" 
                     src="${channel.stream_icon || 'assets/images/default-channel.png'}" 
                     alt="${this.escapeHtml(channel.name)}"
                     onerror="if(this.src.indexOf('default-channel.png')===-1){this.src='assets/images/default-channel.png';}else{this.style.display='none';}">
                <div class="channel-info">
                    <h4>${this.escapeHtml(channel.name)}</h4>
                    <div class="channel-category">${this.escapeHtml(channel.category_name || '')}</div>
                </div>
            </div>
        `).join('');
    }

    renderMovies(movies) {
        if (!Array.isArray(movies)) return '<div class="error">No se pudieron cargar las películas</div>';
        
        return movies.map(movie => `
            <div class="movie-card" data-movie-id="${movie.stream_id}">
                <img class="movie-poster" 
                     src="${movie.stream_icon || 'assets/images/default-movie.png'}" 
                     alt="${this.escapeHtml(movie.name)}"
                     onerror="if(this.src.indexOf('default-movie.png')===-1){this.src='assets/images/default-movie.png';}else{this.style.display='none';}">
                <div class="movie-info">
                    <h4>${this.escapeHtml(movie.name)}</h4>
                    <div class="movie-year">${movie.year || 'N/A'}</div>
                    ${movie.rating ? `<div class="movie-rating">⭐ ${movie.rating}</div>` : ''}
                </div>
            </div>
        `).join('');
    }

    renderSeries(series) {
        if (!Array.isArray(series)) return '<div class="error">No se pudieron cargar las series</div>';
        
        return series.map(serie => `
            <div class="series-card" data-series-id="${serie.series_id}">
                <img class="series-poster" 
                     src="${serie.cover || 'assets/images/default-series.png'}" 
                     alt="${this.escapeHtml(serie.name)}"
                     onerror="if(this.src.indexOf('default-series.png')===-1){this.src='assets/images/default-series.png';}else{this.style.display='none';}">
                <div class="series-info">
                    <h4>${this.escapeHtml(serie.name)}</h4>
                    <div class="series-year">${serie.year || 'N/A'}</div>
                    ${serie.seasons ? `<div class="series-seasons">${serie.seasons} temporadas</div>` : ''}
                </div>
            </div>
        `).join('');
    }

    addBackButton(container, onClickCallback) {
        const backBtn = document.createElement('button');
        backBtn.innerHTML = '<i class="fas fa-arrow-left"></i> Volver';
        backBtn.className = 'back-btn';
        backBtn.style.cssText = 'margin-bottom: 1rem; padding: 0.5rem 1rem; background: #4CAF50; color: white; border: none; border-radius: 5px; cursor: pointer;';
        backBtn.addEventListener('click', onClickCallback);
        container.insertBefore(backBtn, container.firstChild);
    }

    async showMovieDetail(movieId) {
        const modal = document.getElementById('movie-detail-modal');
        const content = document.getElementById('movie-detail-content');
        
        content.innerHTML = '<div class="loading"><i class="fas fa-spinner fa-spin"></i> Cargando información...</div>';
        this.showModal(modal);

        try {
            const response = await fetch(`api/xtream.php?action=get_movie_info&vod_id=${movieId}`);
            const movieInfo = await response.json();

            if (movieInfo.error) {
                throw new Error(movieInfo.error);
            }

            content.innerHTML = this.renderMovieDetail(movieInfo);
            
            // Add play button event
            const playBtn = content.querySelector('.play-button');
            if (playBtn) {
                playBtn.addEventListener('click', () => {
                    this.playMovie(movieId, movieInfo.info?.name || 'Película');
                    this.closeModal(modal);
                });
            }

        } catch (error) {
            content.innerHTML = `<div class="error">Error al cargar información: ${error.message}</div>`;
        }
    }

    async showSeriesDetail(seriesId) {
        const modal = document.getElementById('series-detail-modal');
        const content = document.getElementById('series-detail-content');
        
        content.innerHTML = '<div class="loading"><i class="fas fa-spinner fa-spin"></i> Cargando información...</div>';
        this.showModal(modal);

        try {
            const response = await fetch(`api/xtream.php?action=get_series_info&series_id=${seriesId}`);
            const seriesInfo = await response.json();

            if (seriesInfo.error) {
                throw new Error(seriesInfo.error);
            }

            content.innerHTML = this.renderSeriesDetail(seriesInfo);
            this.setupSeriesNavigation(content, seriesInfo);

        } catch (error) {
            content.innerHTML = `<div class="error">Error al cargar información: ${error.message}</div>`;
        }
    }

    renderMovieDetail(movieInfo) {
        const info = movieInfo.info || {};
        const movie = movieInfo.movie_data || {};
        
        return `
            <div class="detail-content">
                <div class="detail-header">
                    <img class="detail-poster" 
                         src="${info.movie_image || 'assets/images/default-movie.png'}" 
                         alt="${this.escapeHtml(info.name || 'Película')}"
                         onerror="if(this.src.indexOf('default-movie.png')===-1){this.src='assets/images/default-movie.png';}else{this.style.display='none';}">
                    <div class="detail-info">
                        <h2>${this.escapeHtml(info.name || 'Película')}</h2>
                        <div class="detail-meta">
                            <span>Año: ${info.year || 'N/A'}</span>
                            <span>Duración: ${info.duration || 'N/A'}</span>
                            <span>Género: ${info.genre || 'N/A'}</span>
                        </div>
                        ${info.rating ? `<div class="detail-rating">⭐ ${info.rating}/10</div>` : ''}
                        <div class="detail-description">
                            ${this.escapeHtml(info.plot || 'Sin descripción disponible')}
                        </div>
                        <button class="play-button">
                            <i class="fas fa-play"></i> Reproducir
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    renderSeriesDetail(seriesInfo) {
        const info = seriesInfo.info || {};
        const seasons = seriesInfo.seasons || {};
        
        return `
            <div class="detail-content">
                <div class="detail-header">
                    <img class="detail-poster" 
                         src="${info.cover || 'assets/images/default-series.png'}" 
                         alt="${this.escapeHtml(info.name || 'Serie')}"
                         onerror="if(this.src.indexOf('default-series.png')===-1){this.src='assets/images/default-series.png';}else{this.style.display='none';}">
                    <div class="detail-info">
                        <h2>${this.escapeHtml(info.name || 'Serie')}</h2>
                        <div class="detail-meta">
                            <span>Año: ${info.year || 'N/A'}</span>
                            <span>Temporadas: ${Object.keys(seasons).length}</span>
                            <span>Género: ${info.genre || 'N/A'}</span>
                        </div>
                        ${info.rating ? `<div class="detail-rating">⭐ ${info.rating}/10</div>` : ''}
                        <div class="detail-description">
                            ${this.escapeHtml(info.plot || 'Sin descripción disponible')}
                        </div>
                    </div>
                </div>
                
                <div class="seasons-container">
                    <h3>Temporadas</h3>
                    <div class="seasons-grid">
                        ${Object.keys(seasons).map(seasonNum => `
                            <div class="season-card" data-season="${seasonNum}">
                                <h4>Temporada ${seasonNum}</h4>
                                <div>${seasons[seasonNum].length} episodios</div>
                            </div>
                        `).join('')}
                    </div>
                    
                    ${Object.keys(seasons).map(seasonNum => `
                        <div class="episodes-container" data-season="${seasonNum}">
                            <h4>Temporada ${seasonNum} - Episodios</h4>
                            <div class="episodes-grid">
                                ${seasons[seasonNum].map(episode => `
                                    <div class="episode-item" 
                                         data-stream-id="${episode.id}"
                                         data-episode-title="${this.escapeHtml(episode.title)}">
                                        <div class="episode-number">E${episode.episode_num}</div>
                                        <div class="episode-title">${this.escapeHtml(episode.title)}</div>
                                        <div class="episode-duration">${episode.info?.duration || 'N/A'}</div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    setupSeriesNavigation(container, seriesInfo) {
        const seasons = seriesInfo.seasons || {};
        
        // Season navigation
        container.querySelectorAll('.season-card').forEach(card => {
            card.addEventListener('click', () => {
                const season = card.dataset.season;
                
                // Update active season
                container.querySelectorAll('.season-card').forEach(c => c.classList.remove('active'));
                card.classList.add('active');
                
                // Show episodes for this season
                container.querySelectorAll('.episodes-container').forEach(ec => {
                    ec.classList.remove('active');
                });
                container.querySelector(`[data-season="${season}"].episodes-container`).classList.add('active');
            });
        });

        // Episode click events
        container.querySelectorAll('.episode-item').forEach(item => {
            item.addEventListener('click', () => {
                const streamId = item.dataset.streamId;
                const episodeTitle = item.dataset.episodeTitle;
                const seriesName = seriesInfo.info?.name || 'Serie';
                
                // Prepare episode list for player
                const currentSeason = item.closest('.episodes-container').dataset.season;
                this.currentEpisodes = seasons[currentSeason] || [];
                this.currentEpisodeIndex = this.currentEpisodes.findIndex(ep => ep.id == streamId);
                
                this.playEpisode(streamId, `${seriesName} - ${episodeTitle}`);
                this.closeModal(container.closest('.modal'));
            });
        });

        // Activate first season by default
        if (Object.keys(seasons).length > 0) {
            const firstSeason = Object.keys(seasons)[0];
            const firstSeasonCard = container.querySelector(`[data-season="${firstSeason}"].season-card`);
            if (firstSeasonCard) {
                firstSeasonCard.click();
            }
        }
    }

    async playLiveChannel(streamId, channelName) {
        try {
            const response = await fetch(`api/xtream.php?action=get_stream_url&type=live&stream_id=${streamId}&extension=m3u8`);
            const data = await response.json();
            
            if (data.error) {
                throw new Error(data.error);
            }

            this.setupPlayer(data.url, channelName, 'live');
        } catch (error) {
            this.showError('Error al cargar el canal: ' + error.message);
        }
    }

    async playMovie(movieId, movieName) {
        try {
            const response = await fetch(`api/xtream.php?action=get_stream_url&type=movie&stream_id=${movieId}`);
            const data = await response.json();
            
            if (data.error) {
                throw new Error(data.error);
            }

            this.setupPlayer(data.url, movieName, 'movie');
        } catch (error) {
            this.showError('Error al cargar la película: ' + error.message);
        }
    }

    async playEpisode(episodeId, episodeTitle) {
        try {
            const response = await fetch(`api/xtream.php?action=get_stream_url&type=series&stream_id=${episodeId}`);
            const data = await response.json();
            
            if (data.error) {
                throw new Error(data.error);
            }

            this.setupPlayer(data.url, episodeTitle, 'series');
        } catch (error) {
            this.showError('Error al cargar el episodio: ' + error.message);
        }
    }

    setupPlayer(url, title, type) {
        const modal = document.getElementById('video-player-modal');
        const video = document.getElementById('main-video');
        const videoSource = document.getElementById('video-source');
        const playerTitle = document.getElementById('player-title');
        const episodeList = document.getElementById('episode-list');
        const nextEpisodeBtn = document.getElementById('next-episode-btn');

        // Limpiar reproductor anterior
        if (this.currentHls) {
            this.currentHls.destroy();
            this.currentHls = null;
        }

        // Set title
        playerTitle.textContent = title;

        // Show/hide episode controls
        if (type === 'series' && this.currentEpisodes.length > 0) {
            episodeList.style.display = 'block';
            nextEpisodeBtn.style.display = 'inline-block';
            this.renderEpisodeList();
        } else {
            episodeList.style.display = 'none';
            nextEpisodeBtn.style.display = 'none';
        }

        // Configurar reproductor según el tipo de URL
        this.configureVideoPlayer(video, videoSource, url);

        this.showModal(modal);
        
        // Setup custom controls
        this.setupCustomControls(video);
    }

    configureVideoPlayer(video, videoSource, url) {
        // Limpiar estado anterior
        video.pause();
        video.removeAttribute('src');
        video.load();

        console.log('Configurando reproductor para URL:', url);

        // Determinar el tipo de stream y configurar apropiadamente
        const urlPath = url.split('?')[0].toLowerCase();

        if (urlPath.includes('.m3u8') || url.includes('playlist.m3u8')) {
            console.log('Detectado stream HLS');
            this.setupHLS(video, url);
        } else if (this.isDirectVideoFormat(urlPath)) {
            console.log('Detectado video directo:', this.getVideoType(url));
            this.setupDirectVideo(video, videoSource, url);
        } else if (url.includes('stream_proxy.php')) {
            console.log('Detectado proxy stream');
            this.setupDirectVideo(video, videoSource, url);
        } else {
            console.log('Tipo de stream desconocido, intentando HLS primero...');
            // Intentar HLS primero, luego video directo como fallback
            this.setupHLSWithFallback(video, videoSource, url);
        }
    }

    isDirectVideoFormat(url) {
        const videoExtensions = ['.mp4', '.webm', '.ogg', '.ogv', '.mkv', '.avi', '.mov', '.flv', '.m4v', '.3gp', '.wmv', '.asf', '.ts'];
        return videoExtensions.some(ext => url.includes(ext));
    }

    setupHLSWithFallback(video, videoSource, url) {
        console.log('Intentando HLS con fallback a video directo...');

        if (Hls.isSupported()) {
            const adjustedUrl = this.adjustUrlForMixedContent(url);
            const hls = new Hls({
                enableWorker: true,
                lowLatencyMode: true,
                backBufferLength: 90,
                maxBufferLength: 30,
                maxMaxBufferLength: 60,
                startLevel: -1,
                debug: false,
                xhrSetup: function(xhr, url) {
                    xhr.withCredentials = false;
                    xhr.setRequestHeader('Accept', '*/*');
                    xhr.timeout = 10000; // Timeout más corto para fallback rápido
                }
            });

            let hlsFailed = false;

            hls.on(Hls.Events.MANIFEST_PARSED, () => {
                console.log('HLS manifest loaded, playing...');
                video.play().catch(e => console.log('Autoplay prevented:', e));
            });

            hls.on(Hls.Events.ERROR, (event, data) => {
                if (data.fatal && !hlsFailed) {
                    hlsFailed = true;
                    console.log('HLS failed, falling back to direct video...');
                    hls.destroy();
                    this.setupDirectVideo(video, videoSource, url);
                }
            });

            // Timeout para fallback si HLS no carga
            setTimeout(() => {
                if (!hlsFailed && hls && video.readyState === 0) {
                    console.log('HLS timeout, falling back to direct video...');
                    hlsFailed = true;
                    hls.destroy();
                    this.setupDirectVideo(video, videoSource, url);
                }
            }, 8000);

            hls.loadSource(adjustedUrl);
            hls.attachMedia(video);
            this.currentHls = hls;
        } else {
            // Si HLS no está soportado, usar video directo
            console.log('HLS not supported, using direct video...');
            this.setupDirectVideo(video, videoSource, url);
        }
    }

    setupDirectVideo(video, videoSource, url) {
        const adjustedUrl = this.adjustUrlForMixedContent(url);

        // Configurar atributos del video para mejor compatibilidad
        video.crossOrigin = 'anonymous';
        video.preload = 'metadata';
        video.playsInline = true; // Para dispositivos móviles

        // Intentar múltiples formatos/estrategias
        this.tryVideoPlayback(video, videoSource, adjustedUrl, url);
    }

    tryVideoPlayback(video, videoSource, primaryUrl, fallbackUrl, attempt = 1) {
        console.log(`Intento ${attempt} de reproducción:`, primaryUrl);

        videoSource.src = primaryUrl;
        videoSource.type = this.getVideoType(primaryUrl);

        video.load();

        // Timeout para detectar si la carga falla
        const loadTimeout = setTimeout(() => {
            console.warn('Timeout en carga de video, intentando fallback...');
            this.handleVideoFallback(video, videoSource, primaryUrl, fallbackUrl, attempt);
        }, 15000);

        const onLoadedMetadata = () => {
            clearTimeout(loadTimeout);
            console.log('Video metadata loaded successfully');
            video.removeEventListener('loadedmetadata', onLoadedMetadata);
            video.removeEventListener('error', onError);

            video.play().catch(e => {
                console.log('Autoplay prevented:', e);
                // Mostrar botón de play si autoplay falla
                const playBtn = document.getElementById('play-pause-btn');
                if (playBtn) playBtn.style.display = 'block';
            });
        };

        const onError = (e) => {
            clearTimeout(loadTimeout);
            console.error('Error en reproducción directa:', e);
            video.removeEventListener('loadedmetadata', onLoadedMetadata);
            video.removeEventListener('error', onError);

            this.handleVideoFallback(video, videoSource, primaryUrl, fallbackUrl, attempt);
        };

        video.addEventListener('loadedmetadata', onLoadedMetadata);
        video.addEventListener('error', onError);
    }

    handleVideoFallback(video, videoSource, primaryUrl, fallbackUrl, attempt) {
        if (attempt === 1 && fallbackUrl && primaryUrl !== fallbackUrl) {
            // Intentar con URL original
            console.log('Intentando con URL original...');
            this.tryVideoPlayback(video, videoSource, fallbackUrl, null, 2);
        } else if (attempt === 2 && primaryUrl.includes('stream_proxy.php')) {
            // Si el proxy falló, intentar URL directa
            const directUrl = new URLSearchParams(primaryUrl.split('?')[1]).get('url');
            if (directUrl) {
                console.log('Proxy falló, intentando URL directa...');
                this.tryVideoPlayback(video, videoSource, directUrl, null, 3);
            } else {
                this.showVideoError(video);
            }
        } else {
            // Todos los intentos fallaron
            this.showVideoError(video);
        }
    }

    showVideoError(video) {
        console.error('Todos los intentos de reproducción fallaron');
        this.showError('No se pudo reproducir el video. Verifica tu conexión e intenta nuevamente.');

        // Mostrar mensaje en el video
        const errorDiv = document.createElement('div');
        errorDiv.className = 'video-error-message';
        errorDiv.innerHTML = `
            <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);
                        background: rgba(0,0,0,0.8); color: white; padding: 20px; border-radius: 8px; text-align: center;">
                <h3>Error de Reproducción</h3>
                <p>No se pudo cargar el video</p>
                <button onclick="location.reload()" style="background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer;">
                    Recargar Página
                </button>
            </div>
        `;

        const videoContainer = video.parentElement;
        if (videoContainer) {
            videoContainer.appendChild(errorDiv);
        }
    }

    getVideoType(url) {
        // Extraer extensión de la URL
        const urlPath = url.split('?')[0]; // Remover query parameters
        const extension = urlPath.split('.').pop().toLowerCase();

        // Mapeo de extensiones a tipos MIME
        const mimeTypes = {
            'mp4': 'video/mp4',
            'webm': 'video/webm',
            'ogg': 'video/ogg',
            'ogv': 'video/ogg',
            'mkv': 'video/x-matroska',
            'avi': 'video/x-msvideo',
            'mov': 'video/quicktime',
            'flv': 'video/x-flv',
            'm3u8': 'application/x-mpegURL',
            'ts': 'video/mp2t',
            'm4v': 'video/mp4',
            '3gp': 'video/3gpp',
            'wmv': 'video/x-ms-wmv',
            'asf': 'video/x-ms-asf'
        };

        return mimeTypes[extension] || 'video/mp4'; // fallback a MP4
    }

    setupHLS(video, url) {
        // Convertir URL HTTP a HTTPS si es necesario para evitar mixed content
        const adjustedUrl = this.adjustUrlForMixedContent(url);
        
        if (Hls.isSupported()) {
            const hls = new Hls({
                enableWorker: true,
                lowLatencyMode: true,
                backBufferLength: 90,
                maxBufferLength: 30,
                maxMaxBufferLength: 60,
                startLevel: -1,
                debug: false,
                xhrSetup: function(xhr, url) {
                    // Configuración para CORS y mixed content
                    xhr.withCredentials = false;

                    // Headers para streaming
                    xhr.setRequestHeader('Accept', '*/*');
                    xhr.setRequestHeader('Cache-Control', 'no-cache');

                    // Timeout más largo para streams
                    xhr.timeout = 30000;
                },
                manifestLoadingTimeOut: 30000,
                manifestLoadingMaxRetry: 3,
                manifestLoadingRetryDelay: 1000,
                levelLoadingTimeOut: 30000,
                levelLoadingMaxRetry: 3,
                levelLoadingRetryDelay: 1000,
                fragLoadingTimeOut: 30000,
                fragLoadingMaxRetry: 3,
                fragLoadingRetryDelay: 1000
            });
            
            hls.loadSource(adjustedUrl);
            hls.attachMedia(video);
            
            hls.on(Hls.Events.MANIFEST_PARSED, function() {
                console.log('HLS manifest loaded successfully');
                video.play().catch(e => {
                    console.log('Autoplay prevented, user interaction required:', e);
                    // Mostrar botón de play si autoplay falla
                    const playBtn = document.getElementById('play-pause-btn');
                    if (playBtn) playBtn.click();
                });
            });
            
            hls.on(Hls.Events.ERROR, function(event, data) {
                console.warn('HLS Event:', event, data);
                if (data.fatal) {
                    switch(data.type) {
                        case Hls.ErrorTypes.NETWORK_ERROR:
                            console.log('Network error, attempting to restart...');
                            hls.startLoad();
                            break;
                        case Hls.ErrorTypes.MEDIA_ERROR:
                            console.log('Media error, attempting to recover...');
                            hls.recoverMediaError();
                            break;
                        default:
                            console.log('Fatal error, trying fallback...');
                            // Intentar con la URL original si falló con la ajustada
                            if (adjustedUrl !== url) {
                                hls.destroy();
                                video.src = url;
                                video.load();
                                video.play().catch(e => console.log('Fallback autoplay prevented:', e));
                            } else {
                                hls.destroy();
                            }
                            break;
                    }
                } else {
                    // Non-fatal errors - just log them
                    console.log('Non-fatal HLS error:', data.details);
                }
            });
            
            this.currentHls = hls;
        } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
            // Safari native HLS support
            video.src = adjustedUrl;
            video.addEventListener('loadedmetadata', function() {
                video.play().catch(e => console.log('Autoplay prevented:', e));
            });
        } else {
            // Fallback para navegadores sin soporte HLS
            console.log('HLS not supported, trying direct video playback');
            video.src = adjustedUrl;
            video.load();
            video.play().catch(e => console.log('Direct playback autoplay prevented:', e));
        }
    }

    adjustUrlForMixedContent(url) {
        // Si estamos en HTTPS, las URLs ya vienen del proxy
        if (window.location.protocol === 'https:') {
            // Si la URL ya es del proxy local, usarla directamente
            if (url.startsWith('api/stream_proxy.php')) {
                console.log('Using proxy URL:', url);
                return url;
            }

            // Si es una URL HTTP externa, debería haber sido convertida por el backend
            if (url.startsWith('http://')) {
                console.warn('HTTP URL detected in HTTPS context, this should have been handled by the backend:', url);
                // Como fallback, intentar convertir a HTTPS
                const httpsUrl = url.replace('http://', 'https://');
                console.log('Fallback: Converting HTTP to HTTPS:', url, '->', httpsUrl);
                return httpsUrl;
            }
        }

        console.log('Using original URL:', url);
        return url;
    }

    setupCustomControls(video) {
        const playPauseBtn = document.getElementById('play-pause-btn');
        const backwardBtn = document.getElementById('backward-btn');
        const forwardBtn = document.getElementById('forward-btn');
        const nextEpisodeBtn = document.getElementById('next-episode-btn');
        const volumeBtn = document.getElementById('volume-btn');
        const volumeSlider = document.getElementById('volume-slider');
        const progressSlider = document.getElementById('progress-slider');
        const currentTimeSpan = document.getElementById('current-time');
        const durationSpan = document.getElementById('duration');
        const fullscreenBtn = document.getElementById('fullscreen-btn');

        // Play/Pause
        playPauseBtn.addEventListener('click', () => {
            if (video.paused) {
                video.play();
                playPauseBtn.innerHTML = '<i class="fas fa-pause"></i>';
            } else {
                video.pause();
                playPauseBtn.innerHTML = '<i class="fas fa-play"></i>';
            }
        });

        // Backward/Forward
        backwardBtn.addEventListener('click', () => {
            video.currentTime = Math.max(0, video.currentTime - 10);
        });

        forwardBtn.addEventListener('click', () => {
            video.currentTime = Math.min(video.duration, video.currentTime + 10);
        });

        // Next Episode
        nextEpisodeBtn.addEventListener('click', () => {
            this.playNextEpisode();
        });

        // Volume
        volumeSlider.addEventListener('input', (e) => {
            video.volume = e.target.value / 100;
            this.updateVolumeIcon(video.volume);
        });

        volumeBtn.addEventListener('click', () => {
            video.muted = !video.muted;
            this.updateVolumeIcon(video.muted ? 0 : video.volume);
        });

        // Progress
        progressSlider.addEventListener('input', (e) => {
            const time = (e.target.value / 100) * video.duration;
            video.currentTime = time;
        });

        // Fullscreen
        fullscreenBtn.addEventListener('click', () => {
            if (document.fullscreenElement) {
                document.exitFullscreen();
            } else {
                video.requestFullscreen();
            }
        });

        // Video events
        video.addEventListener('timeupdate', () => {
            if (video.duration) {
                const progress = (video.currentTime / video.duration) * 100;
                progressSlider.value = progress;
                
                currentTimeSpan.textContent = this.formatTime(video.currentTime);
                durationSpan.textContent = this.formatTime(video.duration);
            }
        });

        video.addEventListener('play', () => {
            playPauseBtn.innerHTML = '<i class="fas fa-pause"></i>';
        });

        video.addEventListener('pause', () => {
            playPauseBtn.innerHTML = '<i class="fas fa-play"></i>';
        });

        video.addEventListener('ended', () => {
            if (this.currentEpisodes.length > 0 && this.currentEpisodeIndex < this.currentEpisodes.length - 1) {
                this.playNextEpisode();
            }
        });

        // Keyboard controls
        video.addEventListener('keydown', (e) => {
            this.handleVideoKeyboard(e, video);
        });
    }

    updateVolumeIcon(volume) {
        const volumeBtn = document.getElementById('volume-btn');
        if (volume === 0) {
            volumeBtn.innerHTML = '<i class="fas fa-volume-mute"></i>';
        } else if (volume < 0.5) {
            volumeBtn.innerHTML = '<i class="fas fa-volume-down"></i>';
        } else {
            volumeBtn.innerHTML = '<i class="fas fa-volume-up"></i>';
        }
    }

    playNextEpisode() {
        if (this.currentEpisodeIndex < this.currentEpisodes.length - 1) {
            this.currentEpisodeIndex++;
            const nextEpisode = this.currentEpisodes[this.currentEpisodeIndex];
            this.playEpisode(nextEpisode.id, nextEpisode.title);
        }
    }

    renderEpisodeList() {
        const episodeList = document.getElementById('episode-list');
        
        episodeList.innerHTML = this.currentEpisodes.map((episode, index) => `
            <div class="episode-item ${index === this.currentEpisodeIndex ? 'active' : ''}" 
                 data-episode-index="${index}">
                <div class="episode-number">E${episode.episode_num}</div>
                <div class="episode-title">${this.escapeHtml(episode.title)}</div>
                <div class="episode-duration">${episode.info?.duration || 'N/A'}</div>
            </div>
        `).join('');

        // Add click events
        episodeList.querySelectorAll('.episode-item').forEach(item => {
            item.addEventListener('click', () => {
                const index = parseInt(item.dataset.episodeIndex);
                this.currentEpisodeIndex = index;
                const episode = this.currentEpisodes[index];
                this.playEpisode(episode.id, episode.title);
            });
        });
    }

    async searchContent(query, type) {
        if (query.length < 2) return;

        try {
            const response = await fetch(`api/xtream.php?action=search&q=${encodeURIComponent(query)}&type=${type}`);
            const results = await response.json();

            if (type === 'movies') {
                const moviesContainer = document.getElementById('movies-list');
                moviesContainer.innerHTML = this.renderMovies(results);
                moviesContainer.style.display = 'grid';
                document.getElementById('movies-categories').style.display = 'none';
                
                // Add click events
                moviesContainer.querySelectorAll('.movie-card').forEach(card => {
                    card.addEventListener('click', () => {
                        const movieId = card.dataset.movieId;
                        this.showMovieDetail(movieId);
                    });
                });
            } else if (type === 'series') {
                const seriesContainer = document.getElementById('series-list');
                seriesContainer.innerHTML = this.renderSeries(results);
                seriesContainer.style.display = 'grid';
                document.getElementById('series-categories').style.display = 'none';
                
                // Add click events
                seriesContainer.querySelectorAll('.series-card').forEach(card => {
                    card.addEventListener('click', () => {
                        const seriesId = card.dataset.seriesId;
                        this.showSeriesDetail(seriesId);
                    });
                });
            }

        } catch (error) {
            console.error('Search error:', error);
        }
    }

    handleKeyboardShortcuts(e) {
        // Global shortcuts
        switch(e.key) {
            case 'Escape':
                // Close any open modal
                const openModal = document.querySelector('.modal[style*="block"]');
                if (openModal) {
                    this.closeModal(openModal);
                }
                break;
            case '1':
                if (e.ctrlKey) {
                    e.preventDefault();
                    this.switchSection('live-tv');
                }
                break;
            case '2':
                if (e.ctrlKey) {
                    e.preventDefault();
                    this.switchSection('movies');
                }
                break;
            case '3':
                if (e.ctrlKey) {
                    e.preventDefault();
                    this.switchSection('series');
                }
                break;
        }
    }

    handleVideoKeyboard(e, video) {
        switch(e.key) {
            case ' ':
                e.preventDefault();
                if (video.paused) {
                    video.play();
                } else {
                    video.pause();
                }
                break;
            case 'ArrowLeft':
                e.preventDefault();
                video.currentTime = Math.max(0, video.currentTime - 10);
                break;
            case 'ArrowRight':
                e.preventDefault();
                video.currentTime = Math.min(video.duration, video.currentTime + 10);
                break;
            case 'ArrowUp':
                e.preventDefault();
                video.volume = Math.min(1, video.volume + 0.1);
                break;
            case 'ArrowDown':
                e.preventDefault();
                video.volume = Math.max(0, video.volume - 0.1);
                break;
            case 'f':
                e.preventDefault();
                if (document.fullscreenElement) {
                    document.exitFullscreen();
                } else {
                    video.requestFullscreen();
                }
                break;
            case 'm':
                e.preventDefault();
                video.muted = !video.muted;
                break;
        }
    }

    showModal(modal) {
        modal.style.display = 'block';
        document.body.style.overflow = 'hidden';
    }

    closeModal(modal) {
        modal.style.display = 'none';
        document.body.style.overflow = 'auto';
        
        // Stop video if it's the player modal
        if (modal.id === 'video-player-modal') {
            const video = document.getElementById('main-video');
            video.pause();
            video.currentTime = 0;
            
            // Destroy HLS instance
            if (this.currentHls) {
                this.currentHls.destroy();
                this.currentHls = null;
            }
        }
    }

    showError(message) {
        // Simple error notification
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-notification';
        errorDiv.textContent = message;
        errorDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #f44336;
            color: white;
            padding: 1rem;
            border-radius: 5px;
            z-index: 10000;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        `;
        
        document.body.appendChild(errorDiv);
        
        setTimeout(() => {
            errorDiv.remove();
        }, 5000);
    }

    formatTime(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);
        
        if (hours > 0) {
            return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        } else {
            return `${minutes}:${secs.toString().padStart(2, '0')}`;
        }
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.iptvApp = new IPTVApp();
});
