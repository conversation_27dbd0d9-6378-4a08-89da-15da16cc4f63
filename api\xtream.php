<?php
session_start();
require_once '../includes/config.php';

// Verificar autenticación
if (!isset($_SESSION['user_info']) || !isset($_SESSION['server_info'])) {
    http_response_code(401);
    echo json_encode(['error' => 'No autorizado']);
    exit();
}

require_once '../includes/xtream_api.php';

// Configurar headers para JSON
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

// Obtener acción
$action = $_GET['action'] ?? $_POST['action'] ?? '';

if (empty($action)) {
    http_response_code(400);
    echo json_encode(['error' => 'Acción no especificada']);
    exit();
}

try {
    $api = new XtreamAPI();
    $api->setServerUrl($_SESSION['server_info']['url']); // Configurar servidor de la sesión
    $api->setCredentials($_SESSION['user_info']['username'], $_SESSION['user_info']['password']);
    
    switch ($action) {
        case 'get_live_categories':
            $categories = $api->getLiveTVCategories();
            echo json_encode($categories);
            break;
            
        case 'get_live_streams':
            $category_id = $_GET['category_id'] ?? null;
            $streams = $api->getLiveStreams($category_id);
            echo json_encode($streams);
            break;
            
        case 'get_movie_categories':
            $categories = $api->getMovieCategories();
            echo json_encode($categories);
            break;
            
        case 'get_movies':
            $category_id = $_GET['category_id'] ?? null;
            $movies = $api->getMovies($category_id);
            echo json_encode($movies);
            break;
            
        case 'get_movie_info':
            $vod_id = $_GET['vod_id'] ?? '';
            if (empty($vod_id)) {
                throw new Exception('ID de película requerido');
            }
            $info = $api->getMovieInfo($vod_id);
            echo json_encode($info);
            break;
            
        case 'get_series_categories':
            $categories = $api->getSeriesCategories();
            echo json_encode($categories);
            break;
            
        case 'get_series':
            $category_id = $_GET['category_id'] ?? null;
            $series = $api->getSeries($category_id);
            echo json_encode($series);
            break;
            
        case 'get_series_info':
            $series_id = $_GET['series_id'] ?? '';
            if (empty($series_id)) {
                throw new Exception('ID de serie requerido');
            }
            $info = $api->getSeriesInfo($series_id);
            echo json_encode($info);
            break;
            
        case 'get_stream_url':
            $type = $_GET['type'] ?? '';
            $stream_id = $_GET['stream_id'] ?? '';
            $extension = $_GET['extension'] ?? '';

            if (empty($type) || empty($stream_id)) {
                throw new Exception('Parámetros requeridos: type, stream_id');
            }

            $url = '';
            switch ($type) {
                case 'live':
                    $url = $api->getLiveStreamUrl($stream_id, $extension ?: 'ts');
                    break;
                case 'movie':
                    $url = $api->getMovieStreamUrl($stream_id, $extension ?: 'mp4');
                    break;
                case 'series':
                    $url = $api->getSeriesStreamUrl($stream_id, $extension ?: 'mp4');
                    break;
                default:
                    throw new Exception('Tipo de stream no válido');
            }

            // Si estamos en HTTPS, usar proxy local para evitar contenido mixto
            if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
                $proxyUrl = 'api/stream_proxy.php?url=' . urlencode($url) . '&redirect=1';
                echo json_encode(['url' => $proxyUrl]);
            } else {
                echo json_encode(['url' => $url]);
            }
            break;
            
        case 'search':
            $query = $_GET['q'] ?? '';
            $type = $_GET['type'] ?? 'all';
            
            if (empty($query)) {
                echo json_encode([]);
                break;
            }
            
            $results = [];
            
            if ($type === 'all' || $type === 'movies') {
                $movies = $api->getMovies();
                if ($movies && is_array($movies)) {
                    foreach ($movies as $movie) {
                        if (stripos($movie['name'], $query) !== false) {
                            $movie['type'] = 'movie';
                            $results[] = $movie;
                        }
                    }
                }
            }
            
            if ($type === 'all' || $type === 'series') {
                $series = $api->getSeries();
                if ($series && is_array($series)) {
                    foreach ($series as $serie) {
                        if (stripos($serie['name'], $query) !== false) {
                            $serie['type'] = 'series';
                            $results[] = $serie;
                        }
                    }
                }
            }
            
            echo json_encode($results);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Acción no válida']);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}
?>
