/* Video Player Styles */
.video-controls-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 100;
}

.video-controls-overlay.show {
    opacity: 1;
}

.video-controls {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

/* Loading Spinner */
.video-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.8);
    padding: 2rem;
    border-radius: 10px;
    pointer-events: auto;
}

.loading-spinner i {
    font-size: 2rem;
    color: #4CAF50;
}

/* Play/Pause Overlay */
.play-pause-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    pointer-events: auto;
}

.play-pause-center {
    background: rgba(0, 0, 0, 0.7);
    border: none;
    color: white;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    font-size: 2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.play-pause-center:hover {
    background: rgba(0, 0, 0, 0.9);
    transform: scale(1.1);
}

/* Bottom Controls */
.bottom-controls {
    pointer-events: auto;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    padding: 2rem 1rem 1rem;
}

/* Progress Bar */
.progress-container {
    position: relative;
    margin-bottom: 1rem;
}

.progress-bar {
    height: 6px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    cursor: pointer;
    position: relative;
    transition: height 0.2s ease;
}

.progress-bar:hover {
    height: 8px;
}

.progress-buffer {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 3px;
    transition: width 0.3s ease;
}

.progress-played {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: #4CAF50;
    border-radius: 3px;
    transition: width 0.1s ease;
}

.progress-handle {
    position: absolute;
    top: 50%;
    width: 16px;
    height: 16px;
    background: #4CAF50;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
    transition: opacity 0.2s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.progress-bar:hover .progress-handle {
    opacity: 1;
}

.time-tooltip {
    position: absolute;
    bottom: 100%;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    transform: translateX(-50%);
    margin-bottom: 0.5rem;
    display: none;
    white-space: nowrap;
}

.time-tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.8);
}

/* Control Buttons */
.control-buttons {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.left-controls,
.right-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.control-buttons button {
    background: transparent;
    border: none;
    color: white;
    padding: 0.5rem;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.9rem;
}

.control-buttons button:hover {
    background: rgba(255, 255, 255, 0.2);
}

.control-buttons button:active {
    transform: scale(0.95);
}

.btn-label {
    font-size: 0.8rem;
    display: none;
}

@media (min-width: 768px) {
    .btn-label {
        display: inline;
    }
}

/* Volume Control */
.volume-control {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    position: relative;
}

.volume-slider-container {
    width: 0;
    overflow: hidden;
    transition: width 0.3s ease;
}

.volume-control:hover .volume-slider-container {
    width: 80px;
}

.volume-slider {
    width: 80px;
    padding: 0.5rem 0;
}

.volume-track {
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
    position: relative;
    cursor: pointer;
}

.volume-fill {
    height: 100%;
    background: #4CAF50;
    border-radius: 2px;
    transition: width 0.1s ease;
}

.volume-handle {
    position: absolute;
    top: 50%;
    width: 12px;
    height: 12px;
    background: #4CAF50;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    cursor: pointer;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);
}

/* Time Display */
.time-display {
    color: white;
    font-size: 0.9rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    margin: 0 1rem;
}

.separator {
    opacity: 0.7;
}

/* Button Groups with Dropdowns */
.btn-group {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    bottom: 100%;
    right: 0;
    background: rgba(0, 0, 0, 0.9);
    border-radius: 6px;
    padding: 0.5rem 0;
    margin-bottom: 0.5rem;
    min-width: 150px;
    max-height: 200px;
    overflow-y: auto;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.btn-group.open .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.menu-item {
    padding: 0.75rem 1rem;
    color: white;
    cursor: pointer;
    transition: background 0.2s ease;
    font-size: 0.9rem;
}

.menu-item:hover {
    background: rgba(255, 255, 255, 0.1);
}

.menu-item.active {
    background: rgba(76, 175, 80, 0.3);
    color: #4CAF50;
}

.menu-item.active::after {
    content: '✓';
    float: right;
}

/* Custom Scrollbar for Dropdown Menus */
.dropdown-menu::-webkit-scrollbar {
    width: 6px;
}

.dropdown-menu::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

.dropdown-menu::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

.dropdown-menu::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .bottom-controls {
        padding: 1rem 0.5rem 0.5rem;
    }
    
    .control-buttons {
        flex-wrap: wrap;
        gap: 0.5rem;
    }
    
    .left-controls,
    .right-controls {
        gap: 0.25rem;
    }
    
    .control-buttons button {
        padding: 0.4rem;
        font-size: 0.8rem;
    }
    
    .time-display {
        font-size: 0.8rem;
        margin: 0 0.5rem;
    }
    
    .volume-control:hover .volume-slider-container {
        width: 60px;
    }
    
    .volume-slider {
        width: 60px;
    }
    
    .play-pause-center {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
}

@media (max-width: 480px) {
    .bottom-controls {
        padding: 0.5rem 0.25rem 0.25rem;
    }
    
    .progress-container {
        margin-bottom: 0.5rem;
    }
    
    .control-buttons {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .left-controls,
    .right-controls {
        width: 100%;
        justify-content: space-around;
    }
    
    .control-buttons button {
        padding: 0.3rem;
    }
    
    .dropdown-menu {
        right: auto;
        left: 50%;
        transform: translateX(-50%) translateY(10px);
    }
    
    .btn-group.open .dropdown-menu {
        transform: translateX(-50%) translateY(0);
    }
}

/* Touch-friendly hover states */
@media (pointer: coarse) {
    .progress-handle {
        opacity: 1;
        width: 20px;
        height: 20px;
    }
    
    .volume-handle {
        width: 16px;
        height: 16px;
    }
    
    .control-buttons button {
        padding: 0.75rem;
        min-width: 44px;
        min-height: 44px;
        justify-content: center;
    }
    
    .play-pause-center {
        width: 100px;
        height: 100px;
        font-size: 2.5rem;
    }
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.video-controls-overlay.show {
    animation: fadeIn 0.3s ease;
}

.control-buttons button:active {
    animation: pulse 0.2s ease;
}

/* Focus states for accessibility */
.control-buttons button:focus {
    outline: 2px solid #4CAF50;
    outline-offset: 2px;
}

.progress-bar:focus,
.volume-track:focus {
    outline: 2px solid #4CAF50;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .progress-bar {
        background: rgba(255, 255, 255, 0.8);
    }
    
    .progress-played,
    .progress-handle,
    .volume-fill,
    .volume-handle {
        background: #ffffff;
    }
    
    .control-buttons button {
        border: 1px solid rgba(255, 255, 255, 0.5);
    }
    
    .dropdown-menu {
        border: 2px solid rgba(255, 255, 255, 0.5);
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .video-controls-overlay,
    .control-buttons button,
    .progress-bar,
    .volume-slider-container,
    .dropdown-menu {
        transition: none;
    }
    
    .loading-spinner i {
        animation: none;
    }
}
