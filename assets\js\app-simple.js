class SimpleIPTVPlayer {
    constructor() {
        this.currentHls = null;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadCategories();
    }

    setupEventListeners() {
        // Navigation tabs
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', (e) => {
                this.switchSection(e.currentTarget.dataset.section);
            });
        });

        // Close modal
        document.querySelectorAll('.close-modal').forEach(btn => {
            btn.addEventListener('click', () => {
                this.closeVideoModal();
            });
        });

        // Modal click outside
        document.getElementById('video-player-modal').addEventListener('click', (e) => {
            if (e.target.id === 'video-player-modal') {
                this.closeVideoModal();
            }
        });
    }

    switchSection(section) {
        // Update active tab
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-section="${section}"]`).classList.add('active');

        // Update active content
        document.querySelectorAll('.content-section').forEach(section => {
            section.classList.remove('active');
        });
        document.getElementById(section).classList.add('active');

        // Load content
        if (section === 'live-tv') {
            this.loadCategories();
        } else if (section === 'movies') {
            this.loadMovieCategories();
        } else if (section === 'series') {
            this.loadSeriesCategories();
        }
    }

    async loadCategories() {
        const container = document.getElementById('live-categories');
        container.innerHTML = '<div class="loading"><i class="fas fa-spinner fa-spin"></i> Cargando categorías...</div>';

        try {
            const response = await fetch('/api/xtream.php?action=get_live_categories');
            const categories = await response.json();

            if (Array.isArray(categories) && categories.length > 0) {
                container.innerHTML = categories.map(cat => `
                    <div class="category-card" onclick="player.loadChannels('${cat.category_id}', '${cat.category_name}')">
                        <div class="category-icon">
                            <i class="fas fa-tv"></i>
                        </div>
                        <h3>${cat.category_name}</h3>
                        <p>Ver canales</p>
                    </div>
                `).join('');
            } else {
                container.innerHTML = '<div class="error">No se encontraron categorías</div>';
            }
        } catch (error) {
            console.error('Error loading categories:', error);
            container.innerHTML = '<div class="error">Error al cargar categorías</div>';
        }
    }

    async loadChannels(categoryId, categoryName) {
        const container = document.getElementById('live-channels');
        const categoriesContainer = document.getElementById('live-categories');
        
        categoriesContainer.style.display = 'none';
        container.style.display = 'grid';
        
        container.innerHTML = `
            <div class="back-button" onclick="player.showCategories()">
                <i class="fas fa-arrow-left"></i> Volver a Categorías
            </div>
            <div class="loading"><i class="fas fa-spinner fa-spin"></i> Cargando canales de ${categoryName}...</div>
        `;

        try {
            const response = await fetch(`/api/xtream.php?action=get_live_streams&category_id=${categoryId}`);
            const channels = await response.json();

            if (Array.isArray(channels) && channels.length > 0) {
                const backButton = container.querySelector('.back-button');
                container.innerHTML = '';
                container.appendChild(backButton);
                
                channels.forEach(channel => {
                    const channelCard = document.createElement('div');
                    channelCard.className = 'channel-card';
                    channelCard.innerHTML = `
                        <div class="channel-icon">
                            <i class="fas fa-play-circle"></i>
                        </div>
                        <h4>${channel.name}</h4>
                        <p>Canal en vivo</p>
                    `;
                    channelCard.onclick = () => this.playChannel(channel.stream_id, channel.name);
                    container.appendChild(channelCard);
                });
            } else {
                container.innerHTML = `
                    <div class="back-button" onclick="player.showCategories()">
                        <i class="fas fa-arrow-left"></i> Volver a Categorías
                    </div>
                    <div class="error">No se encontraron canales en esta categoría</div>
                `;
            }
        } catch (error) {
            console.error('Error loading channels:', error);
            container.innerHTML = `
                <div class="back-button" onclick="player.showCategories()">
                    <i class="fas fa-arrow-left"></i> Volver a Categorías
                </div>
                <div class="error">Error al cargar canales</div>
            `;
        }
    }

    showCategories() {
        document.getElementById('live-categories').style.display = 'grid';
        document.getElementById('live-channels').style.display = 'none';
    }

    async playChannel(streamId, channelName) {
        console.log('Playing channel:', channelName, 'ID:', streamId);
        
        try {
            const response = await fetch(`/api/xtream.php?action=get_stream_url&stream_id=${streamId}&type=live`);
            let data;
            try {
                data = await response.json();
            } catch (parseError) {
                // If response is plain text URL
                data = await response.text();
            }

            let streamUrl;
            if (typeof data === 'string') {
                streamUrl = data;
            } else if (data.url) {
                streamUrl = data.url;
            }
            if (streamUrl) {
                console.log('Stream URL obtained:', streamUrl);
                this.setupVideo(streamUrl, channelName);
            } else {
                const errorMsg = (typeof data === 'object' && (data.error || data.message)) || 'Error desconocido';
                console.error('Failed to get stream URL:', errorMsg);
                this.showError('No se pudo obtener la URL del stream: ' + errorMsg);
            }
        } catch (error) {
            console.error('Error getting stream URL:', error);
            this.showError('Error al obtener la URL del stream');
        }
    }

    setupVideo(url, title) {
        const modal = document.getElementById('video-player-modal');
        const video = document.getElementById('main-video');
        const playerTitle = document.getElementById('player-title');

        // Clean previous player
        if (this.currentHls) {
            this.currentHls.destroy();
            this.currentHls = null;
        }

        video.pause();
        video.removeAttribute('src');
        video.load();

        playerTitle.textContent = title;

        console.log('Setting up video with URL:', url);

        // Try HLS first
        if (Hls.isSupported()) {
            this.currentHls = new Hls({
                debug: true,
                enableWorker: true,
                lowLatencyMode: true,
                backBufferLength: 90,
                maxBufferLength: 30,
                startLevel: -1,
                xhrSetup: function(xhr, url) {
                    xhr.withCredentials = false;
                    xhr.timeout = 10000;
                }
            });

            this.currentHls.loadSource(url);
            this.currentHls.attachMedia(video);

            this.currentHls.on(Hls.Events.MANIFEST_PARSED, () => {
                console.log('HLS manifest parsed successfully');
                video.play().catch(e => {
                    console.log('Autoplay prevented:', e);
                });
            });

            this.currentHls.on(Hls.Events.ERROR, (event, data) => {
                console.error('HLS Error:', data);
                if (data.fatal) {
                    this.showError('Error de reproducción HLS: ' + data.details);
                }
            });

        } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
            console.log('Using native HLS support');
            video.src = url;
            video.addEventListener('loadedmetadata', () => {
                console.log('Native HLS loaded');
                video.play().catch(e => {
                    console.log('Autoplay prevented:', e);
                });
            });
            video.addEventListener('error', (e) => {
                console.error('Native video error:', e);
                this.showError('Error de reproducción nativa');
            });
        } else {
            this.showError('HLS no soportado en este navegador');
            return;
        }

        // Show modal
        modal.style.display = 'flex';
    }

    showError(message) {
        const modal = document.getElementById('video-player-modal');
        const video = document.getElementById('main-video');
        
        video.innerHTML = `
            <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center; color: white; background: rgba(0,0,0,0.8); padding: 20px; border-radius: 10px;">
                <i class="fas fa-exclamation-triangle" style="font-size: 48px; color: #ff6b6b; margin-bottom: 15px;"></i>
                <h3>Error de Reproducción</h3>
                <p>${message}</p>
            </div>
        `;
        
        modal.style.display = 'flex';
    }

    closeVideoModal() {
        const modal = document.getElementById('video-player-modal');
        const video = document.getElementById('main-video');

        if (this.currentHls) {
            this.currentHls.destroy();
            this.currentHls = null;
        }

        video.pause();
        video.removeAttribute('src');
        video.load();
        modal.style.display = 'none';
    }

    async loadMovieCategories() {
        const container = document.getElementById('movies-categories');
        container.innerHTML = '<div class="loading"><i class="fas fa-spinner fa-spin"></i> Cargando categorías de películas...</div>';
        
        try {
            console.log('Fetching movie categories...');
            const response = await fetch('/api/xtream.php?action=get_movie_categories');
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const categories = await response.json();
            console.log('Movie categories response:', categories);

            if (categories && categories.error) {
                throw new Error(categories.error);
            }

            if (Array.isArray(categories) && categories.length > 0) {
                container.innerHTML = categories.map(cat => `
                    <div class="category-card" onclick="player.loadMovies('${cat.category_id}', '${cat.category_name}')">
                        <div class="category-icon">
                            <i class="fas fa-film"></i>
                        </div>
                        <h3>${cat.category_name}</h3>
                        <p>Ver películas</p>
                    </div>
                `).join('');
            } else if (Array.isArray(categories) && categories.length === 0) {
                container.innerHTML = '<div class="error">No hay categorías de películas disponibles</div>';
            } else {
                console.error('Invalid categories response:', categories);
                container.innerHTML = '<div class="error">Respuesta inválida del servidor</div>';
            }
        } catch (error) {
            console.error('Error loading movie categories:', error);
            container.innerHTML = `<div class="error">Error al cargar categorías de películas: ${error.message}</div>`;
        }
    }

    async loadSeriesCategories() {
        const container = document.getElementById('series-categories');
        container.innerHTML = '<div class="loading"><i class="fas fa-spinner fa-spin"></i> Cargando series...</div>';
        
        try {
            const response = await fetch('/api/xtream.php?action=get_series_categories');
            const categories = await response.json();

            if (Array.isArray(categories) && categories.length > 0) {
                container.innerHTML = categories.map(cat => `
                    <div class="category-card">
                        <div class="category-icon">
                            <i class="fas fa-video"></i>
                        </div>
                        <h3>${cat.category_name}</h3>
                        <p>Próximamente</p>
                    </div>
                `).join('');
            } else {
                container.innerHTML = '<div class="error">No se encontraron categorías de series</div>';
            }
        } catch (error) {
            console.error('Error loading series categories:', error);
            container.innerHTML = '<div class="error">Error al cargar categorías de series</div>';
        }
    }

    async loadMovies(categoryId, categoryName) {
        const container = document.getElementById('movies-categories');
        
        container.innerHTML = `
            <div class="back-button" onclick="player.loadMovieCategories()">
                <i class="fas fa-arrow-left"></i> Volver a Categorías
            </div>
            <div class="loading"><i class="fas fa-spinner fa-spin"></i> Cargando películas de ${categoryName}...</div>
        `;

        try {
            console.log('Fetching movies for category:', categoryId);
            const response = await fetch(`/api/xtream.php?action=get_movies&category_id=${categoryId}`);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const movies = await response.json();
            console.log('Movies response:', movies);

            if (movies && movies.error) {
                throw new Error(movies.error);
            }

            if (Array.isArray(movies) && movies.length > 0) {
                const backButton = container.querySelector('.back-button');
                container.innerHTML = '';
                container.appendChild(backButton);
                
                movies.forEach(movie => {
                    const movieCard = document.createElement('div');
                    movieCard.className = 'channel-card';
                    movieCard.innerHTML = `
                        <div class="channel-icon">
                            <i class="fas fa-play-circle"></i>
                        </div>
                        <h4>${movie.name}</h4>
                        <p>Película</p>
                    `;
                    movieCard.onclick = () => this.playMovie(movie.stream_id, movie.name);
                    container.appendChild(movieCard);
                });
            } else if (Array.isArray(movies) && movies.length === 0) {
                container.innerHTML = `
                    <div class="back-button" onclick="player.loadMovieCategories()">
                        <i class="fas fa-arrow-left"></i> Volver a Categorías
                    </div>
                    <div class="error">No hay películas disponibles en esta categoría</div>
                `;
            } else {
                container.innerHTML = `
                    <div class="back-button" onclick="player.loadMovieCategories()">
                        <i class="fas fa-arrow-left"></i> Volver a Categorías
                    </div>
                    <div class="error">Respuesta inválida del servidor</div>
                `;
            }
        } catch (error) {
            console.error('Error loading movies:', error);
            container.innerHTML = `
                <div class="back-button" onclick="player.loadMovieCategories()">
                    <i class="fas fa-arrow-left"></i> Volver a Categorías
                </div>
                <div class="error">Error al cargar películas: ${error.message}</div>
            `;
        }
    }

    async playMovie(streamId, movieName) {
        console.log('Playing movie:', movieName, 'ID:', streamId);
        
        try {
            const response = await fetch(`/api/xtream.php?action=get_stream_url&stream_id=${streamId}&type=movie`);
            const data = await response.json();

            if (data.success && data.url) {
                console.log('Movie stream URL obtained:', data.url);
                this.setupVideo(data.url, movieName);
            } else {
                console.error('Failed to get movie stream URL:', data.message);
                this.showError('No se pudo obtener la URL de la película: ' + (data.message || 'Error desconocido'));
            }
        } catch (error) {
            console.error('Error getting movie stream URL:', error);
            this.showError('Error al obtener la URL de la película');
        }
    }
}

// Initialize player when DOM is loaded
let player;
document.addEventListener('DOMContentLoaded', () => {
    player = new SimpleIPTVPlayer();
    console.log('Simple IPTV Player initialized');
});
