/* Imágenes por defecto y placeholders */

/* Imagen por defecto para canales */
.channel-logo[src="assets/images/default-channel.png"],
.channel-logo[src=""],
.channel-logo:not([src]),
.channel-logo[style*="display: none"] {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    display: flex !important;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    font-weight: bold;
    position: relative;
}

.channel-logo[src="assets/images/default-channel.png"]::before,
.channel-logo[src=""]::before,
.channel-logo:not([src])::before,
.channel-logo[style*="display: none"]::before {
    content: "📺";
    font-size: 1.5rem;
}

/* Imagen por defecto para películas */
.movie-poster[src="assets/images/default-movie.png"],
.movie-poster[src=""],
.movie-poster:not([src]),
.movie-poster[style*="display: none"] {
    background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%) !important;
    display: flex !important;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    position: relative;
}

.movie-poster[src="assets/images/default-movie.png"]::before,
.movie-poster[src=""]::before,
.movie-poster:not([src])::before,
.movie-poster[style*="display: none"]::before {
    content: "🎬";
    font-size: 3rem;
}

/* Imagen por defecto para series */
.series-poster[src="assets/images/default-series.png"],
.series-poster[src=""],
.series-poster:not([src]),
.series-poster[style*="display: none"] {
    background: linear-gradient(135deg, #9C27B0 0%, #7B1FA2 100%) !important;
    display: flex !important;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    position: relative;
}

.series-poster[src="assets/images/default-series.png"]::before,
.series-poster[src=""]::before,
.series-poster:not([src])::before,
.series-poster[style*="display: none"]::before {
    content: "📺";
    font-size: 3rem;
}

/* Imagen por defecto para detalles */
.detail-poster[src="assets/images/default-movie.png"],
.detail-poster[src="assets/images/default-series.png"],
.detail-poster[src=""],
.detail-poster:not([src]) {
    background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 3rem;
    position: relative;
}

.detail-poster[src="assets/images/default-movie.png"]::before,
.detail-poster[src=""]::before,
.detail-poster:not([src])::before {
    content: "🎭";
    font-size: 4rem;
}

.detail-poster[src="assets/images/default-series.png"]::before {
    content: "📺";
    font-size: 4rem;
}

/* Animación de carga para imágenes */
.movie-poster,
.series-poster,
.channel-logo,
.detail-poster {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.channel-logo[data-loading="true"],
.movie-poster[data-loading="true"],
.series-poster[data-loading="true"],
.detail-poster[data-loading="true"] {
    opacity: 0.7;
    background-color: #f0f0f0;
    animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
    0% {
        opacity: 0.7;
    }
    50% {
        opacity: 0.4;
    }
    100% {
        opacity: 0.7;
    }
}

/* Estilo para elementos sin imagen */
.no-image {
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #333 0%, #555 100%);
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.8rem;
    text-align: center;
    padding: 1rem;
}

.no-image i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    display: block;
}
